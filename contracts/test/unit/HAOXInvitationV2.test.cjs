const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("HAOXInvitationV2", function () {
  let haoxToken;
  let invitationContract;
  let owner;
  let inviter;
  let invitee1;
  let invitee2;
  let addr1;

  beforeEach(async function () {
    [owner, inviter, invitee1, invitee2, addr1] = await ethers.getSigners();

    // 部署HAOX代币
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();

    // 部署邀请合约
    const HAOXInvitationV2 = await ethers.getContractFactory("HAOXInvitationV2");
    invitationContract = await HAOXInvitationV2.deploy(
      await haoxToken.getAddress()
    );
    await invitationContract.waitForDeployment();

    // 向邀请合约转入代币用于奖励
    const rewardTokens = ethers.parseEther("10000000"); // 1000万代币
    await haoxToken.transfer(await invitationContract.getAddress(), rewardTokens);
  });

  describe("Deployment", function () {
    it("Should set the correct token address", async function () {
      expect(await invitationContract.haoxToken()).to.equal(await haoxToken.getAddress());
    });

    it("Should set owner correctly", async function () {
      expect(await invitationContract.owner()).to.equal(owner.address);
    });

    it("Should have correct initial state", async function () {
      expect(await invitationContract.totalInvitations()).to.equal(0);
      expect(await invitationContract.totalRewardsDistributed()).to.equal(0);
    });

    it("Should have correct reward constants", async function () {
      expect(await invitationContract.BASE_REWARD()).to.equal(ethers.parseEther("100"));
      expect(await invitationContract.LEVEL_MULTIPLIER()).to.equal(150); // 1.5x
      expect(await invitationContract.MAX_LEVEL()).to.equal(5);
      expect(await invitationContract.MIN_PURCHASE_FOR_REWARD()).to.equal(ethers.parseEther("0.1"));
    });
  });

  describe("Invitation Management", function () {
    it("Should allow users to create invitations", async function () {
      await expect(invitationContract.connect(inviter).createInvitation())
        .to.emit(invitationContract, "InvitationCreated")
        .withArgs(inviter.address, 1);
      
      const invitation = await invitationContract.invitations(1);
      expect(invitation.inviter).to.equal(inviter.address);
      expect(invitation.active).to.be.true;
      expect(invitation.totalInvitees).to.equal(0);
      expect(invitation.totalRewards).to.equal(0);
    });

    it("Should assign unique invitation codes", async function () {
      await invitationContract.connect(inviter).createInvitation();
      await invitationContract.connect(invitee1).createInvitation();
      
      const invitation1 = await invitationContract.invitations(1);
      const invitation2 = await invitationContract.invitations(2);
      
      expect(invitation1.inviter).to.equal(inviter.address);
      expect(invitation2.inviter).to.equal(invitee1.address);
      expect(await invitationContract.totalInvitations()).to.equal(2);
    });

    it("Should prevent creating multiple invitations per user", async function () {
      await invitationContract.connect(inviter).createInvitation();
      
      await expect(invitationContract.connect(inviter).createInvitation())
        .to.be.revertedWith("Already has invitation");
    });

    it("Should allow owner to deactivate invitations", async function () {
      await invitationContract.connect(inviter).createInvitation();
      
      await expect(invitationContract.deactivateInvitation(1))
        .to.emit(invitationContract, "InvitationDeactivated")
        .withArgs(1);
      
      const invitation = await invitationContract.invitations(1);
      expect(invitation.active).to.be.false;
    });

    it("Should prevent non-owner from deactivating invitations", async function () {
      await invitationContract.connect(inviter).createInvitation();
      
      await expect(invitationContract.connect(addr1).deactivateInvitation(1))
        .to.be.revertedWithCustomError(invitationContract, "OwnableUnauthorizedAccount");
    });
  });

  describe("Invitation Usage", function () {
    beforeEach(async function () {
      // 创建邀请码
      await invitationContract.connect(inviter).createInvitation();
    });

    it("Should allow users to use invitation codes", async function () {
      const purchaseAmount = ethers.parseEther("1"); // 1 BNB
      
      await expect(invitationContract.connect(invitee1).useInvitation(1, purchaseAmount))
        .to.emit(invitationContract, "InvitationUsed")
        .withArgs(1, invitee1.address, purchaseAmount);
      
      const invitation = await invitationContract.invitations(1);
      expect(invitation.totalInvitees).to.equal(1);
      
      expect(await invitationContract.userInviter(invitee1.address)).to.equal(inviter.address);
      expect(await invitationContract.userInvitationCode(invitee1.address)).to.equal(1);
    });

    it("Should prevent using invalid invitation codes", async function () {
      const purchaseAmount = ethers.parseEther("1");
      
      await expect(invitationContract.connect(invitee1).useInvitation(999, purchaseAmount))
        .to.be.revertedWith("Invalid invitation");
    });

    it("Should prevent using inactive invitation codes", async function () {
      await invitationContract.deactivateInvitation(1);
      const purchaseAmount = ethers.parseEther("1");
      
      await expect(invitationContract.connect(invitee1).useInvitation(1, purchaseAmount))
        .to.be.revertedWith("Invitation not active");
    });

    it("Should prevent using invitation codes multiple times", async function () {
      const purchaseAmount = ethers.parseEther("1");
      
      await invitationContract.connect(invitee1).useInvitation(1, purchaseAmount);
      
      await expect(invitationContract.connect(invitee1).useInvitation(1, purchaseAmount))
        .to.be.revertedWith("Already used invitation");
    });

    it("Should prevent self-invitation", async function () {
      const purchaseAmount = ethers.parseEther("1");
      
      await expect(invitationContract.connect(inviter).useInvitation(1, purchaseAmount))
        .to.be.revertedWith("Cannot invite yourself");
    });

    it("Should enforce minimum purchase amount", async function () {
      const smallAmount = ethers.parseEther("0.05"); // 小于最小购买量
      
      await expect(invitationContract.connect(invitee1).useInvitation(1, smallAmount))
        .to.be.revertedWith("Purchase too small");
    });
  });

  describe("Reward Calculation and Distribution", function () {
    beforeEach(async function () {
      await invitationContract.connect(inviter).createInvitation();
      await invitationContract.connect(invitee1).useInvitation(1, ethers.parseEther("1"));
    });

    it("Should calculate correct base rewards", async function () {
      const purchaseAmount = ethers.parseEther("1");
      const expectedReward = await invitationContract.calculateReward(1, purchaseAmount);
      
      expect(expectedReward).to.equal(ethers.parseEther("100")); // BASE_REWARD
    });

    it("Should calculate level-based rewards correctly", async function () {
      // 创建多级邀请链
      await invitationContract.connect(invitee1).createInvitation();
      await invitationContract.connect(invitee2).useInvitation(2, ethers.parseEther("1"));
      
      const level1Reward = await invitationContract.calculateReward(1, ethers.parseEther("1"));
      const level2Reward = await invitationContract.calculateReward(2, ethers.parseEther("1"));
      
      expect(level1Reward).to.equal(ethers.parseEther("100")); // Level 1
      expect(level2Reward).to.equal(ethers.parseEther("150")); // Level 2: 100 * 1.5
    });

    it("Should distribute rewards correctly", async function () {
      const initialBalance = await haoxToken.balanceOf(inviter.address);
      
      await expect(invitationContract.distributeReward(1, ethers.parseEther("1")))
        .to.emit(invitationContract, "RewardDistributed");
      
      const finalBalance = await haoxToken.balanceOf(inviter.address);
      expect(finalBalance - initialBalance).to.equal(ethers.parseEther("100"));
      
      const invitation = await invitationContract.invitations(1);
      expect(invitation.totalRewards).to.equal(ethers.parseEther("100"));
    });

    it("Should prevent non-owner from distributing rewards", async function () {
      await expect(invitationContract.connect(addr1).distributeReward(1, ethers.parseEther("1")))
        .to.be.revertedWithCustomError(invitationContract, "OwnableUnauthorizedAccount");
    });

    it("Should handle insufficient contract balance", async function () {
      // 提取大部分代币
      const contractBalance = await haoxToken.balanceOf(await invitationContract.getAddress());
      await invitationContract.withdrawTokens(contractBalance - ethers.parseEther("50"));
      
      await expect(invitationContract.distributeReward(1, ethers.parseEther("1")))
        .to.be.revertedWith("Insufficient contract balance");
    });
  });

  describe("Multi-level Invitation System", function () {
    it("Should track invitation levels correctly", async function () {
      // Level 1: inviter -> invitee1
      await invitationContract.connect(inviter).createInvitation();
      await invitationContract.connect(invitee1).useInvitation(1, ethers.parseEther("1"));
      
      // Level 2: invitee1 -> invitee2
      await invitationContract.connect(invitee1).createInvitation();
      await invitationContract.connect(invitee2).useInvitation(2, ethers.parseEther("1"));
      
      expect(await invitationContract.getInvitationLevel(1)).to.equal(1);
      expect(await invitationContract.getInvitationLevel(2)).to.equal(2);
    });

    it("Should enforce maximum invitation levels", async function () {
      // 创建5级邀请链
      let currentInviter = inviter;
      let currentInvitee;
      
      for (let i = 1; i <= 5; i++) {
        await invitationContract.connect(currentInviter).createInvitation();
        
        currentInvitee = await ethers.getSigner(i + 5); // 使用新的地址
        await invitationContract.connect(currentInvitee).useInvitation(i, ethers.parseEther("1"));
        
        currentInviter = currentInvitee;
      }
      
      // 第6级应该被限制
      await invitationContract.connect(currentInviter).createInvitation();
      const level = await invitationContract.getInvitationLevel(6);
      expect(level).to.equal(5); // 应该被限制在最大级别
    });
  });

  describe("Administrative Functions", function () {
    it("Should allow owner to withdraw tokens", async function () {
      const withdrawAmount = ethers.parseEther("1000000");
      const initialBalance = await haoxToken.balanceOf(owner.address);
      
      await expect(invitationContract.withdrawTokens(withdrawAmount))
        .to.emit(invitationContract, "TokensWithdrawn")
        .withArgs(withdrawAmount);
      
      const finalBalance = await haoxToken.balanceOf(owner.address);
      expect(finalBalance - initialBalance).to.equal(withdrawAmount);
    });

    it("Should prevent non-owner from withdrawing tokens", async function () {
      await expect(invitationContract.connect(addr1).withdrawTokens(ethers.parseEther("1000")))
        .to.be.revertedWithCustomError(invitationContract, "OwnableUnauthorizedAccount");
    });

    it("Should allow owner to update reward parameters", async function () {
      const newBaseReward = ethers.parseEther("200");
      
      await expect(invitationContract.updateBaseReward(newBaseReward))
        .to.emit(invitationContract, "BaseRewardUpdated")
        .withArgs(newBaseReward);
      
      expect(await invitationContract.BASE_REWARD()).to.equal(newBaseReward);
    });

    it("Should allow owner to pause and unpause", async function () {
      await invitationContract.pause();
      expect(await invitationContract.paused()).to.be.true;
      
      await expect(invitationContract.connect(inviter).createInvitation())
        .to.be.revertedWithCustomError(invitationContract, "EnforcedPause");
      
      await invitationContract.unpause();
      expect(await invitationContract.paused()).to.be.false;
    });
  });

  describe("View Functions", function () {
    beforeEach(async function () {
      await invitationContract.connect(inviter).createInvitation();
      await invitationContract.connect(invitee1).useInvitation(1, ethers.parseEther("1"));
    });

    it("Should return correct invitation statistics", async function () {
      const stats = await invitationContract.getInvitationStats(1);
      expect(stats.totalInvitees).to.equal(1);
      expect(stats.totalRewards).to.equal(0); // 奖励还未分发
      expect(stats.active).to.be.true;
    });

    it("Should return correct user invitation info", async function () {
      const userInfo = await invitationContract.getUserInvitationInfo(invitee1.address);
      expect(userInfo.hasUsedInvitation).to.be.true;
      expect(userInfo.invitationCode).to.equal(1);
      expect(userInfo.inviter).to.equal(inviter.address);
    });

    it("Should return correct contract statistics", async function () {
      expect(await invitationContract.totalInvitations()).to.equal(1);
      expect(await invitationContract.totalRewardsDistributed()).to.equal(0);
    });
  });

  describe("Edge Cases", function () {
    it("Should handle zero purchase amounts", async function () {
      await invitationContract.connect(inviter).createInvitation();
      
      await expect(invitationContract.connect(invitee1).useInvitation(1, 0))
        .to.be.revertedWith("Purchase too small");
    });

    it("Should handle very large purchase amounts", async function () {
      await invitationContract.connect(inviter).createInvitation();
      const largeAmount = ethers.parseEther("1000000");
      
      await invitationContract.connect(invitee1).useInvitation(1, largeAmount);
      
      const invitation = await invitationContract.invitations(1);
      expect(invitation.totalInvitees).to.equal(1);
    });

    it("Should handle contract with zero token balance", async function () {
      // 提取所有代币
      const contractBalance = await haoxToken.balanceOf(await invitationContract.getAddress());
      await invitationContract.withdrawTokens(contractBalance);
      
      await expect(invitationContract.distributeReward(1, ethers.parseEther("1")))
        .to.be.revertedWith("Insufficient contract balance");
    });
  });
});
