const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("HAOXTokenV2", function () {
  let haoxToken;
  let owner;
  let addr1;
  let addr2;
  let addrs;

  beforeEach(async function () {
    [owner, addr1, addr2, ...addrs] = await ethers.getSigners();

    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();
  });

  describe("Deployment", function () {
    it("Should set the right owner", async function () {
      expect(await haoxToken.owner()).to.equal(owner.address);
    });

    it("Should assign the total supply of tokens to the owner", async function () {
      const ownerBalance = await haoxToken.balanceOf(owner.address);
      expect(await haoxToken.totalSupply()).to.equal(ownerBalance);
    });

    it("Should have correct name and symbol", async function () {
      expect(await haoxToken.name()).to.equal("HAOX Token");
      expect(await haoxToken.symbol()).to.equal("HAOX");
    });

    it("Should have 18 decimals", async function () {
      expect(await haoxToken.decimals()).to.equal(18);
    });

    it("Should have correct total supply", async function () {
      const expectedSupply = ethers.parseEther("*********"); // 5亿代币 (初始解锁)
      expect(await haoxToken.totalSupply()).to.equal(expectedSupply);
    });
  });

  describe("Transactions", function () {
    it("Should transfer tokens between accounts", async function () {
      const transferAmount = ethers.parseEther("50");
      
      await haoxToken.transfer(addr1.address, transferAmount);
      const addr1Balance = await haoxToken.balanceOf(addr1.address);
      expect(addr1Balance).to.equal(transferAmount);

      await haoxToken.connect(addr1).transfer(addr2.address, transferAmount);
      const addr2Balance = await haoxToken.balanceOf(addr2.address);
      expect(addr2Balance).to.equal(transferAmount);
    });

    it("Should fail if sender doesn't have enough tokens", async function () {
      const initialOwnerBalance = await haoxToken.balanceOf(owner.address);
      const transferAmount = initialOwnerBalance + 1n;

      await expect(
        haoxToken.connect(addr1).transfer(owner.address, transferAmount)
      ).to.be.revertedWithCustomError(haoxToken, "ERC20InsufficientBalance");
    });

    it("Should update balances after transfers", async function () {
      const initialOwnerBalance = await haoxToken.balanceOf(owner.address);
      const transferAmount = ethers.parseEther("100");

      await haoxToken.transfer(addr1.address, transferAmount);
      await haoxToken.transfer(addr2.address, transferAmount);

      const finalOwnerBalance = await haoxToken.balanceOf(owner.address);
      expect(finalOwnerBalance).to.equal(initialOwnerBalance - transferAmount * 2n);

      const addr1Balance = await haoxToken.balanceOf(addr1.address);
      expect(addr1Balance).to.equal(transferAmount);

      const addr2Balance = await haoxToken.balanceOf(addr2.address);
      expect(addr2Balance).to.equal(transferAmount);
    });
  });

  describe("Allowances", function () {
    it("Should approve tokens for delegated transfer", async function () {
      const approveAmount = ethers.parseEther("100");
      
      await haoxToken.approve(addr1.address, approveAmount);
      expect(await haoxToken.allowance(owner.address, addr1.address)).to.equal(approveAmount);
    });

    it("Should allow delegated transfers", async function () {
      const approveAmount = ethers.parseEther("100");
      const transferAmount = ethers.parseEther("50");
      
      await haoxToken.approve(addr1.address, approveAmount);
      await haoxToken.connect(addr1).transferFrom(owner.address, addr2.address, transferAmount);
      
      expect(await haoxToken.balanceOf(addr2.address)).to.equal(transferAmount);
      expect(await haoxToken.allowance(owner.address, addr1.address)).to.equal(approveAmount - transferAmount);
    });

    it("Should fail delegated transfer if allowance is insufficient", async function () {
      const approveAmount = ethers.parseEther("50");
      const transferAmount = ethers.parseEther("100");
      
      await haoxToken.approve(addr1.address, approveAmount);
      
      await expect(
        haoxToken.connect(addr1).transferFrom(owner.address, addr2.address, transferAmount)
      ).to.be.revertedWithCustomError(haoxToken, "ERC20InsufficientAllowance");
    });
  });

  describe("Ownership", function () {
    it("Should allow owner to transfer ownership", async function () {
      await haoxToken.transferOwnership(addr1.address);
      expect(await haoxToken.owner()).to.equal(addr1.address);
    });

    it("Should prevent non-owner from transferring ownership", async function () {
      await expect(
        haoxToken.connect(addr1).transferOwnership(addr2.address)
      ).to.be.revertedWithCustomError(haoxToken, "OwnableUnauthorizedAccount");
    });
  });

  describe("Pausable", function () {
    it("Should allow owner to emergency pause and unpause", async function () {
      await haoxToken.emergencyPause();
      expect(await haoxToken.paused()).to.be.true;

      await haoxToken.unpause();
      expect(await haoxToken.paused()).to.be.false;
    });

    it("Should prevent transfers when paused", async function () {
      await haoxToken.emergencyPause();

      await expect(
        haoxToken.transfer(addr1.address, ethers.parseEther("50"))
      ).to.be.revertedWithCustomError(haoxToken, "EnforcedPause");
    });

    it("Should prevent non-owner from emergency pausing", async function () {
      await expect(
        haoxToken.connect(addr1).emergencyPause()
      ).to.be.revertedWithCustomError(haoxToken, "OwnableUnauthorizedAccount");
    });
  });

  describe("Edge Cases", function () {
    it("Should handle zero transfers", async function () {
      await expect(haoxToken.transfer(addr1.address, 0)).to.not.be.reverted;
      expect(await haoxToken.balanceOf(addr1.address)).to.equal(0);
    });

    it("Should handle maximum approval", async function () {
      const maxApproval = ethers.MaxUint256;
      await haoxToken.approve(addr1.address, maxApproval);
      expect(await haoxToken.allowance(owner.address, addr1.address)).to.equal(maxApproval);
    });

    it("Should prevent transfer to zero address", async function () {
      await expect(
        haoxToken.transfer(ethers.ZeroAddress, ethers.parseEther("50"))
      ).to.be.revertedWithCustomError(haoxToken, "ERC20InvalidReceiver");
    });
  });
});
