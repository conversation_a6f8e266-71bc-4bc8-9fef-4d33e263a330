const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("HAOXVestingV2Ultra", function () {
  let haoxToken;
  let priceOracle;
  let vestingContract;
  let owner;
  let projectWallet;
  let communityWallet;
  let addr1;
  let addr2;

  beforeEach(async function () {
    [owner, projectWallet, communityWallet, addr1, addr2] = await ethers.getSigners();

    // 部署HAOX代币
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();

    // 部署模拟价格预言机
    const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
    priceOracle = await MockPriceOracle.deploy();
    await priceOracle.waitForDeployment();

    // 部署Ultra解锁合约
    const HAOXVestingV2Ultra = await ethers.getContractFactory("HAOXVestingV2Ultra");
    vestingContract = await HAOXVestingV2Ultra.deploy(
      await haoxToken.getAddress(),
      await priceOracle.getAddress(),
      projectWallet.address,
      communityWallet.address
    );
    await vestingContract.waitForDeployment();

    // 向解锁合约转入代币
    const totalTokens = ethers.parseEther("400000000"); // 4亿代币
    await haoxToken.transfer(await vestingContract.getAddress(), totalTokens);
  });

  describe("Deployment and Initialization", function () {
    it("Should set correct immutable variables", async function () {
      expect(await vestingContract.haoxToken()).to.equal(await haoxToken.getAddress());
      expect(await vestingContract.priceOracle()).to.equal(await priceOracle.getAddress());
      expect(await vestingContract.projectWallet()).to.equal(projectWallet.address);
      expect(await vestingContract.communityWallet()).to.equal(communityWallet.address);
    });

    it("Should set correct constants", async function () {
      expect(await vestingContract.TOTAL_ROUNDS()).to.equal(31);
      expect(await vestingContract.PRICE_MAINTAIN_DURATION()).to.equal(7 * 24 * 3600);
      expect(await vestingContract.EMERGENCY_DELAY()).to.equal(7 * 24 * 3600);
      expect(await vestingContract.MAX_EMERGENCY_AMOUNT()).to.equal(ethers.parseEther("1000000"));
    });

    it("Should initialize with round 1", async function () {
      expect(await vestingContract.currentRound()).to.equal(1);
    });

    it("Should set owner as emergency signer", async function () {
      expect(await vestingContract.emergencySigners(owner.address)).to.be.true;
    });

    it("Should initialize all 31 rounds with correct prices", async function () {
      for (let i = 1; i <= 31; i++) {
        const round = await vestingContract.rounds(i);
        expect(round.triggerPrice).to.be.gt(0);
        expect(round.priceConditionMet).to.be.false;
        expect(round.unlocked).to.be.false;
        expect(round.priceReachedTime).to.equal(0);
      }
    });

    it("Should have required signatures set to 1", async function () {
      expect(await vestingContract.requiredSignatures()).to.equal(1);
    });
  });

  describe("Price Condition Checking - Ultra Version", function () {
    it("Should check price condition successfully", async function () {
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      
      await expect(vestingContract.checkPriceCondition())
        .to.emit(vestingContract, "PriceConditionMet")
        .withArgs(1, 1000000);
    });

    it("Should not trigger condition if price too low", async function () {
      await priceOracle.setPrice(500000); // 0.005 USD (低于0.01触发价格)
      
      await vestingContract.checkPriceCondition();
      
      const round = await vestingContract.rounds(1);
      expect(round.priceConditionMet).to.be.false;
    });

    it("Should reset condition if price drops", async function () {
      // 首先设置高价格
      await priceOracle.setPrice(1000000); // 0.01 USD
      await vestingContract.checkPriceCondition();
      
      let round = await vestingContract.rounds(1);
      expect(round.priceConditionMet).to.be.true;
      
      // 然后设置低价格
      await priceOracle.setPrice(500000); // 0.005 USD
      await vestingContract.checkPriceCondition();
      
      round = await vestingContract.rounds(1);
      expect(round.priceConditionMet).to.be.false;
      expect(round.priceReachedTime).to.equal(0);
    });

    it("Should handle already unlocked rounds", async function () {
      // 设置价格并解锁第1轮
      await priceOracle.setPrice(1000000);
      await vestingContract.checkPriceCondition();
      await time.increase(7 * 24 * 3600);
      await vestingContract.checkPriceCondition(); // 这会触发解锁
      
      // 再次检查价格条件应该跳过已解锁的轮次
      await vestingContract.checkPriceCondition();
      
      const round = await vestingContract.rounds(1);
      expect(round.unlocked).to.be.true;
    });

    it("Should prevent checking when paused", async function () {
      await vestingContract.pause();
      
      await expect(vestingContract.checkPriceCondition())
        .to.be.revertedWithCustomError(vestingContract, "EnforcedPause");
    });

    it("Should handle rounds beyond total rounds", async function () {
      // 验证超过总轮次时的处理
      const totalRounds = await vestingContract.TOTAL_ROUNDS();
      expect(totalRounds).to.equal(31);
      
      // 这个测试验证合约不会在超过31轮时崩溃
      await priceOracle.setPrice(1000000);
      await vestingContract.checkPriceCondition();
    });
  });

  describe("Round Unlocking - Ultra Version", function () {
    it("Should unlock round after 7 days automatically", async function () {
      // 设置高价格并触发条件
      await priceOracle.setPrice(1000000); // 0.01 USD
      await vestingContract.checkPriceCondition();
      
      // 快进7天
      await time.increase(7 * 24 * 3600);
      
      const initialProjectBalance = await haoxToken.balanceOf(projectWallet.address);
      const initialCommunityBalance = await haoxToken.balanceOf(communityWallet.address);
      
      // 再次检查价格条件，这会触发自动解锁
      await expect(vestingContract.checkPriceCondition())
        .to.emit(vestingContract, "RoundUnlocked")
        .withArgs(1, await time.latest() + 1);
      
      const finalProjectBalance = await haoxToken.balanceOf(projectWallet.address);
      const finalCommunityBalance = await haoxToken.balanceOf(communityWallet.address);
      
      expect(finalProjectBalance).to.be.gt(initialProjectBalance);
      expect(finalCommunityBalance).to.be.gt(initialCommunityBalance);
      
      const round = await vestingContract.rounds(1);
      expect(round.unlocked).to.be.true;
      expect(await vestingContract.currentRound()).to.equal(2);
    });

    it("Should not unlock before 7 days", async function () {
      await priceOracle.setPrice(1000000);
      await vestingContract.checkPriceCondition();
      
      // 只快进3天
      await time.increase(3 * 24 * 3600);
      
      await vestingContract.checkPriceCondition();
      
      const round = await vestingContract.rounds(1);
      expect(round.unlocked).to.be.false;
    });

    it("Should not unlock if price condition not met", async function () {
      await priceOracle.setPrice(500000); // 低价格
      await vestingContract.checkPriceCondition();
      
      await time.increase(7 * 24 * 3600);
      
      await vestingContract.checkPriceCondition();
      
      const round = await vestingContract.rounds(1);
      expect(round.unlocked).to.be.false;
    });

    it("Should distribute correct token amounts", async function () {
      await priceOracle.setPrice(1000000);
      await vestingContract.checkPriceCondition();
      await time.increase(7 * 24 * 3600);
      
      const initialProjectBalance = await haoxToken.balanceOf(projectWallet.address);
      const initialCommunityBalance = await haoxToken.balanceOf(communityWallet.address);
      
      await vestingContract.checkPriceCondition(); // 触发解锁
      
      const finalProjectBalance = await haoxToken.balanceOf(projectWallet.address);
      const finalCommunityBalance = await haoxToken.balanceOf(communityWallet.address);
      
      // 第1轮应该分配8000万代币给项目方和8000万给社区
      expect(finalProjectBalance - initialProjectBalance).to.equal(ethers.parseEther("80000000"));
      expect(finalCommunityBalance - initialCommunityBalance).to.equal(ethers.parseEther("80000000"));
    });
  });

  describe("Emergency Functions - Ultra Version", function () {
    beforeEach(async function () {
      // 紧急提取需要合约处于暂停状态
      await vestingContract.pause();
    });

    it("Should allow owner to request withdrawal", async function () {
      const amount = ethers.parseEther("500000");

      await expect(vestingContract.requestEmergencyWithdraw(await haoxToken.getAddress(), amount))
        .to.emit(vestingContract, "EmergencyWithdrawRequested");
    });

    it("Should not allow non-owner to request withdrawal", async function () {
      const amount = ethers.parseEther("500000");

      await expect(vestingContract.connect(addr1).requestEmergencyWithdraw(await haoxToken.getAddress(), amount))
        .to.be.revertedWithCustomError(vestingContract, "OwnableUnauthorizedAccount");
    });

    it("Should not allow withdrawal above maximum", async function () {
      const amount = ethers.parseEther("2000000"); // 超过最大限制

      await expect(vestingContract.requestEmergencyWithdraw(await haoxToken.getAddress(), amount))
        .to.be.revertedWith("Amount exceeds maximum");
    });

    it("Should execute emergency withdrawal after delay", async function () {
      const amount = ethers.parseEther("500000");

      const tx = await vestingContract.requestEmergencyWithdraw(await haoxToken.getAddress(), amount);
      const receipt = await tx.wait();

      // 从事件中获取requestId
      const event = receipt.logs.find(log => log.fragment?.name === "EmergencyWithdrawRequested");
      const requestId = event.args[0];

      await time.increase(7 * 24 * 3600);

      const initialBalance = await haoxToken.balanceOf(owner.address);

      await expect(vestingContract.executeEmergencyWithdraw(requestId, await haoxToken.getAddress()))
        .to.emit(vestingContract, "EmergencyWithdrawExecuted")
        .withArgs(requestId, amount);

      const finalBalance = await haoxToken.balanceOf(owner.address);
      expect(finalBalance - initialBalance).to.equal(amount);
    });

    it("Should not execute before delay", async function () {
      const amount = ethers.parseEther("500000");

      const tx = await vestingContract.requestEmergencyWithdraw(await haoxToken.getAddress(), amount);
      const receipt = await tx.wait();
      const event = receipt.logs.find(log => log.fragment?.name === "EmergencyWithdrawRequested");
      const requestId = event.args[0];

      await expect(vestingContract.executeEmergencyWithdraw(requestId, await haoxToken.getAddress()))
        .to.be.revertedWith("Time lock not expired");
    });

    it("Should not execute twice", async function () {
      const amount = ethers.parseEther("500000");

      const tx = await vestingContract.requestEmergencyWithdraw(await haoxToken.getAddress(), amount);
      const receipt = await tx.wait();
      const event = receipt.logs.find(log => log.fragment?.name === "EmergencyWithdrawRequested");
      const requestId = event.args[0];

      await time.increase(7 * 24 * 3600);
      await vestingContract.executeEmergencyWithdraw(requestId, await haoxToken.getAddress());

      await expect(vestingContract.executeEmergencyWithdraw(requestId, await haoxToken.getAddress()))
        .to.be.revertedWith("Already executed");
    });
  });
});
