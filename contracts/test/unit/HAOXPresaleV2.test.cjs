const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("HAOXPresaleV2", function () {
  let haoxToken;
  let presaleContract;
  let owner;
  let buyer1;
  let buyer2;
  let projectWallet;

  beforeEach(async function () {
    [owner, buyer1, buyer2, projectWallet] = await ethers.getSigners();

    // 部署HAOX代币
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();

    // 部署预售合约
    const HAOXPresaleV2 = await ethers.getContractFactory("HAOXPresaleV2");
    presaleContract = await HAOXPresaleV2.deploy(
      await haoxToken.getAddress(),
      projectWallet.address
    );
    await presaleContract.waitForDeployment();

    // 向预售合约转入代币
    const presaleTokens = ethers.parseEther("200000000"); // 2亿代币
    await haoxToken.transfer(await presaleContract.getAddress(), presaleTokens);
  });

  describe("Deployment", function () {
    it("Should set the correct initial values", async function () {
      expect(await presaleContract.haoxToken()).to.equal(await haoxToken.getAddress());
      expect(await presaleContract.projectWallet()).to.equal(projectWallet.address);
      expect(await presaleContract.owner()).to.equal(owner.address);
    });

    it("Should have correct presale parameters", async function () {
      expect(await presaleContract.TOTAL_PRESALE_TOKENS()).to.equal(ethers.parseEther("200000000"));
      expect(await presaleContract.TARGET_BNB()).to.equal(ethers.parseEther("320"));
      expect(await presaleContract.TOTAL_STAGES()).to.equal(100);
      expect(await presaleContract.MIN_PURCHASE()).to.equal(ethers.parseEther("0.1"));
      expect(await presaleContract.MAX_PURCHASE()).to.equal(ethers.parseEther("10"));
    });

    it("Should initialize with correct stage", async function () {
      expect(await presaleContract.currentStage()).to.equal(1);
      expect(await presaleContract.presaleActive()).to.be.true;
    });
  });

  describe("Token Purchase", function () {
    it("Should allow users to purchase tokens", async function () {
      const purchaseAmount = ethers.parseEther("1"); // 1 BNB
      
      await expect(presaleContract.connect(buyer1).purchaseTokens({ value: purchaseAmount }))
        .to.emit(presaleContract, "TokensPurchased");
      
      const userInfo = await presaleContract.userPurchases(buyer1.address);
      expect(userInfo.bnbContributed).to.equal(purchaseAmount);
      expect(userInfo.tokensAllocated).to.be.gt(0);
    });

    it("Should enforce minimum purchase amount", async function () {
      const tooSmall = ethers.parseEther("0.05"); // 小于最小购买量
      
      await expect(
        presaleContract.connect(buyer1).purchaseTokens({ value: tooSmall })
      ).to.be.revertedWith("Below minimum purchase");
    });

    it("Should enforce maximum purchase amount", async function () {
      const tooLarge = ethers.parseEther("15"); // 大于最大购买量
      
      await expect(
        presaleContract.connect(buyer1).purchaseTokens({ value: tooLarge })
      ).to.be.revertedWith("Exceeds maximum purchase");
    });

    it("Should calculate tokens correctly based on current rate", async function () {
      const purchaseAmount = ethers.parseEther("1");
      const initialRate = await presaleContract.getCurrentRate();
      
      await presaleContract.connect(buyer1).purchaseTokens({ value: purchaseAmount });
      
      const userInfo = await presaleContract.userPurchases(buyer1.address);
      const expectedTokens = purchaseAmount * initialRate / (10n ** 18n);
      expect(userInfo.tokensAllocated).to.equal(expectedTokens);
    });

    it("Should update stage when stage target is reached", async function () {
      const stageTarget = await presaleContract.STAGE_BNB_TARGET();
      const initialStage = await presaleContract.currentStage();
      
      // 购买足够的代币来完成当前阶段
      await presaleContract.connect(buyer1).purchaseTokens({ value: stageTarget });
      
      const newStage = await presaleContract.currentStage();
      expect(newStage).to.be.gt(initialStage);
    });
  });

  describe("Presale Management", function () {
    it("Should allow owner to pause and unpause presale", async function () {
      await presaleContract.pausePresale();
      expect(await presaleContract.presaleActive()).to.be.false;
      
      await presaleContract.resumePresale();
      expect(await presaleContract.presaleActive()).to.be.true;
    });

    it("Should prevent purchases when paused", async function () {
      await presaleContract.pausePresale();
      
      await expect(
        presaleContract.connect(buyer1).purchaseTokens({ value: ethers.parseEther("1") })
      ).to.be.revertedWith("Presale not active");
    });

    it("Should allow owner to end presale", async function () {
      await presaleContract.endPresale();
      expect(await presaleContract.presaleActive()).to.be.false;
      expect(await presaleContract.presaleEnded()).to.be.true;
    });

    it("Should prevent non-owner from managing presale", async function () {
      await expect(
        presaleContract.connect(buyer1).pausePresale()
      ).to.be.revertedWithCustomError(presaleContract, "OwnableUnauthorizedAccount");
    });
  });

  describe("Token Claiming", function () {
    beforeEach(async function () {
      // 购买一些代币
      await presaleContract.connect(buyer1).purchaseTokens({ value: ethers.parseEther("1") });
      await presaleContract.connect(buyer2).purchaseTokens({ value: ethers.parseEther("2") });
      
      // 结束预售
      await presaleContract.endPresale();
    });

    it("Should allow users to claim tokens after presale ends", async function () {
      const initialBalance = await haoxToken.balanceOf(buyer1.address);
      
      await expect(presaleContract.connect(buyer1).claimTokens())
        .to.emit(presaleContract, "TokensClaimed");
      
      const finalBalance = await haoxToken.balanceOf(buyer1.address);
      expect(finalBalance).to.be.gt(initialBalance);
    });

    it("Should prevent claiming before presale ends", async function () {
      // 重新开始预售
      await presaleContract.resumePresale();
      
      await expect(
        presaleContract.connect(buyer1).claimTokens()
      ).to.be.revertedWith("Presale not ended");
    });

    it("Should prevent double claiming", async function () {
      await presaleContract.connect(buyer1).claimTokens();
      
      await expect(
        presaleContract.connect(buyer1).claimTokens()
      ).to.be.revertedWith("Already claimed");
    });

    it("Should prevent claiming with no allocation", async function () {
      await expect(
        presaleContract.connect(owner).claimTokens() // owner没有购买
      ).to.be.revertedWith("No tokens to claim");
    });
  });

  describe("Fund Withdrawal", function () {
    beforeEach(async function () {
      // 购买一些代币
      await presaleContract.connect(buyer1).purchaseTokens({ value: ethers.parseEther("5") });
      await presaleContract.connect(buyer2).purchaseTokens({ value: ethers.parseEther("3") });
    });

    it("Should allow owner to withdraw funds", async function () {
      const contractBalance = await ethers.provider.getBalance(await presaleContract.getAddress());
      const initialProjectBalance = await ethers.provider.getBalance(projectWallet.address);
      
      await presaleContract.withdrawFunds();
      
      const finalProjectBalance = await ethers.provider.getBalance(projectWallet.address);
      expect(finalProjectBalance - initialProjectBalance).to.equal(contractBalance);
    });

    it("Should prevent non-owner from withdrawing funds", async function () {
      await expect(
        presaleContract.connect(buyer1).withdrawFunds()
      ).to.be.revertedWithCustomError(presaleContract, "OwnableUnauthorizedAccount");
    });

    it("Should handle withdrawal of remaining tokens", async function () {
      await presaleContract.endPresale();
      
      const contractTokenBalance = await haoxToken.balanceOf(await presaleContract.getAddress());
      const initialOwnerBalance = await haoxToken.balanceOf(owner.address);
      
      await presaleContract.withdrawRemainingTokens();
      
      const finalOwnerBalance = await haoxToken.balanceOf(owner.address);
      expect(finalOwnerBalance - initialOwnerBalance).to.be.gt(0);
    });
  });

  describe("Emergency Functions", function () {
    it("Should allow emergency pause", async function () {
      await presaleContract.emergencyPause();
      expect(await presaleContract.paused()).to.be.true;
    });

    it("Should prevent operations when emergency paused", async function () {
      await presaleContract.emergencyPause();
      
      await expect(
        presaleContract.connect(buyer1).purchaseTokens({ value: ethers.parseEther("1") })
      ).to.be.revertedWithCustomError(presaleContract, "EnforcedPause");
    });
  });

  describe("View Functions", function () {
    it("Should return correct presale status", async function () {
      const status = await presaleContract.getPresaleStatus();
      expect(status.isActive).to.be.true;
      expect(status.currentStage).to.equal(1);
      expect(status.totalRaised).to.equal(0);
    });

    it("Should return correct user purchase info", async function () {
      await presaleContract.connect(buyer1).purchaseTokens({ value: ethers.parseEther("1") });
      
      const userInfo = await presaleContract.userPurchases(buyer1.address);
      expect(userInfo.bnbContributed).to.equal(ethers.parseEther("1"));
      expect(userInfo.tokensAllocated).to.be.gt(0);
      expect(userInfo.claimed).to.be.false;
    });
  });

  describe("Edge Cases", function () {
    it("Should handle reaching total presale target", async function () {
      const targetBNB = await presaleContract.TARGET_BNB();
      
      // 这个测试需要大量的购买，简化处理
      expect(targetBNB).to.equal(ethers.parseEther("320"));
    });

    it("Should handle stage transitions correctly", async function () {
      const totalStages = await presaleContract.TOTAL_STAGES();
      expect(totalStages).to.equal(100);
    });
  });
});
