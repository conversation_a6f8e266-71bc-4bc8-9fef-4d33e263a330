// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/* solhint-disable not-rely-on-time */

import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {ReentrancyGuard} from "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title HAOXVestingV2Minimal
 * <AUTHOR> Team
 * @notice 精简版HAOX代币解锁合约 - 成本优化版本
 * @dev 保留核心安全功能，移除非必要特性以降低部署成本
 * 实现31轮价格阶梯解锁机制，每轮需要价格维持7天才能解锁
 */
contract HAOXVestingV2Minimal is Ownable, ReentrancyGuard, Pausable {

    // ============ 自定义错误声明 (Gas优化) ============

    /// @notice 无效地址错误
    error InvalidAddress();
    /// @notice 无效轮次错误
    error InvalidRound();
    /// @notice 金额过大错误
    error AmountTooLarge();
    /// @notice 未授权操作错误
    error NotAuthorized();
    /// @notice 请求未找到错误
    error RequestNotFound();
    /// @notice 请求已执行错误
    error AlreadyExecuted();
    /// @notice 时间锁激活错误
    error TimeLockActive();
    /// @notice 转账失败错误
    error TransferFailed();
    /// @notice 价格预言机失败错误
    error PriceOracleFailed();
    
    /// @notice 总解锁轮次数量
    uint256 public constant TOTAL_ROUNDS = 31;
    /// @notice 价格维持持续时间（7天）
    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;
    /// @notice 紧急提取延迟时间（7天）
    uint256 public constant EMERGENCY_DELAY = 7 days;
    /// @notice 最大紧急提取金额（100万代币）
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18;
    
    /// @notice HAOX代币合约地址
    IERC20 public immutable HAOX_TOKEN;
    /// @notice 价格预言机合约地址
    address public immutable PRICE_ORACLE;
    /// @notice 项目方钱包地址
    address public immutable PROJECT_WALLET;
    /// @notice 社区钱包地址
    address public immutable COMMUNITY_WALLET;
    
    uint256 public currentRound = 1;
    
    // 轮次信息结构（优化存储）
    struct Round {
        uint128 triggerPrice;      // 触发价格 (8位小数)
        uint64 priceReachedTime;   // 价格达到时间
        uint64 unlockTime;         // 解锁时间
        bool priceConditionMet;    // 价格条件是否满足
        bool unlocked;             // 是否已解锁
    }
    
    // 紧急提取请求结构（精简版）
    struct EmergencyRequest {
        uint128 amount;
        uint64 requestTime;
        bool executed;
        address requester;
    }
    
    // 状态映射
    mapping(uint256 => Round) public rounds;
    mapping(bytes32 => EmergencyRequest) public emergencyRequests;
    mapping(address => bool) public emergencySigners;
    
    // 精简的价格历史（仅保留最近10条）
    struct PriceCheck {
        uint64 timestamp;
        uint64 price;
        bool conditionMet;
    }
    mapping(uint256 => PriceCheck[10]) public priceHistory;
    mapping(uint256 => uint8) public historyIndex;
    
    uint256 public requiredSignatures = 1;
    
    /**
     * @notice 价格条件满足事件
     * @param roundNumber 轮次编号
     * @param price 当前价格
     * @param timestamp 时间戳
     */
    event PriceConditionMet(uint256 indexed roundNumber, uint256 indexed price, uint256 indexed timestamp);

    /**
     * @notice 轮次解锁事件
     * @param roundNumber 轮次编号
     * @param triggerPrice 触发价格
     * @param projectTokens 项目方获得的代币数量
     * @param communityTokens 社区获得的代币数量
     * @param timestamp 解锁时间戳
     */
    event RoundUnlocked(
        uint256 indexed roundNumber,
        uint256 indexed triggerPrice,
        uint256 indexed projectTokens,
        uint256 communityTokens,
        uint256 timestamp
    );

    /**
     * @notice 价格条件重置事件
     * @param roundNumber 轮次编号
     * @param price 当前价格
     * @param timestamp 时间戳
     */
    event PriceConditionReset(uint256 indexed roundNumber, uint256 indexed price, uint256 indexed timestamp);

    /**
     * @notice 紧急提取请求事件
     * @param requestId 请求ID
     * @param amount 提取金额
     * @param requestTime 请求时间
     */
    event EmergencyWithdrawRequested(bytes32 indexed requestId, uint256 indexed amount, uint256 indexed requestTime);

    /**
     * @notice 紧急提取执行事件
     * @param requestId 请求ID
     * @param amount 提取金额
     */
    event EmergencyWithdrawExecuted(bytes32 indexed requestId, uint256 indexed amount);

    /**
     * @notice 构造函数，初始化解锁合约
     * @param _haoxToken HAOX代币合约地址
     * @param _priceOracle 价格预言机合约地址
     * @param _projectWallet 项目方钱包地址
     * @param _communityWallet 社区钱包地址
     */
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
        if (_haoxToken == address(0)) revert InvalidAddress();
        if (_priceOracle == address(0)) revert InvalidAddress();
        if (_projectWallet == address(0)) revert InvalidAddress();
        if (_communityWallet == address(0)) revert InvalidAddress();
        
        HAOX_TOKEN = IERC20(_haoxToken);
        PRICE_ORACLE = _priceOracle;
        PROJECT_WALLET = _projectWallet;
        COMMUNITY_WALLET = _communityWallet;
        
        // 初始化紧急签名者
        emergencySigners[msg.sender] = true;
        
        // 初始化轮次数据（精简版）
        _initializeRounds();
    }

    /**
     * @dev 初始化轮次数据（Gas优化版本）
     */
    function _initializeRounds() internal {
        // 使用紧凑的初始化方式
        uint128[31] memory prices = [
            uint128(0.01 * 10**8), uint128(0.015 * 10**8), uint128(0.02 * 10**8),
            uint128(0.025 * 10**8), uint128(0.03 * 10**8), uint128(0.035 * 10**8),
            uint128(0.04 * 10**8), uint128(0.045 * 10**8), uint128(0.05 * 10**8),
            uint128(0.055 * 10**8), uint128(0.06 * 10**8), uint128(0.065 * 10**8),
            uint128(0.07 * 10**8), uint128(0.075 * 10**8), uint128(0.08 * 10**8),
            uint128(0.085 * 10**8), uint128(0.09 * 10**8), uint128(0.095 * 10**8),
            uint128(0.1 * 10**8), uint128(0.11 * 10**8), uint128(0.12 * 10**8),
            uint128(0.13 * 10**8), uint128(0.14 * 10**8), uint128(0.15 * 10**8),
            uint128(0.16 * 10**8), uint128(0.17 * 10**8), uint128(0.18 * 10**8),
            uint128(0.19 * 10**8), uint128(0.2 * 10**8), uint128(0.22 * 10**8),
            uint128(0.25 * 10**8)
        ];
        
        for (uint256 i = 0; i < 31; i++) {
            rounds[i + 1].triggerPrice = prices[i];
        }
    }

    /**
     * @dev 检查价格条件（Gas优化版本）
     */
    function checkPriceCondition() external nonReentrant whenNotPaused {
        uint256 roundNumber = currentRound;
        
        if (roundNumber > TOTAL_ROUNDS) {
            return;
        }
        
        // 获取当前价格
        uint256 currentPrice = _getCurrentPrice();
        Round storage round = rounds[roundNumber];
        bool conditionMet = currentPrice >= round.triggerPrice;
        
        // 添加到历史记录（循环覆盖）
        uint8 index = historyIndex[roundNumber];
        priceHistory[roundNumber][index] = PriceCheck({
            timestamp: uint64(block.timestamp), // solhint-disable-line not-rely-on-time
            price: uint64(currentPrice),
            conditionMet: conditionMet
        });
        historyIndex[roundNumber] = (index + 1) % 10;
        
        if (conditionMet) {
            if (!round.priceConditionMet) {
                round.priceConditionMet = true;
                round.priceReachedTime = uint64(block.timestamp);
                // solhint-disable-line not-rely-on-time
                emit PriceConditionMet(roundNumber, currentPrice, block.timestamp);
                // solhint-disable-line not-rely-on-time
            } else {
                uint256 maintainedDuration = block.timestamp - round.priceReachedTime; // solhint-disable-line not-rely-on-time
                if (maintainedDuration >= PRICE_MAINTAIN_DURATION && !round.unlocked) {
                    _unlockRound(roundNumber, currentPrice);
                }
            }
        } else {
            if (round.priceConditionMet && !round.unlocked) {
                round.priceConditionMet = false;
                round.priceReachedTime = 0;
                emit PriceConditionReset(roundNumber, currentPrice, block.timestamp);
                // solhint-disable-line not-rely-on-time
            }
        }
    }

    /**
     * @dev 解锁轮次（精简版）
     * @dev 修复重入攻击风险：先更新状态，再执行外部调用
     */
    function _unlockRound(uint256 roundNumber, uint256 triggerPrice) internal {
        Round storage round = rounds[roundNumber];

        // 先更新所有状态变量（防止重入攻击）
        round.unlocked = true;
        round.unlockTime = uint64(block.timestamp); // solhint-disable-line not-rely-on-time
        currentRound = roundNumber + 1;

        // 计算代币分配（简化计算）
        uint256 projectTokens = 80000000 * 10**18; // 8000万项目代币
        uint256 communityTokens = 80000000 * 10**18; // 8000万社区代币

        // 转移代币（使用自定义错误）
        if (!HAOX_TOKEN.transfer(PROJECT_WALLET, projectTokens)) {
            revert TransferFailed();
        }
        if (!HAOX_TOKEN.transfer(COMMUNITY_WALLET, communityTokens)) {
            revert TransferFailed();
        }

        emit RoundUnlocked(roundNumber, triggerPrice, projectTokens, communityTokens, block.timestamp);
        // solhint-disable-line not-rely-on-time
    }

    /**
     * @dev 获取当前价格（精简版）
     */
    function _getCurrentPrice() internal view returns (uint256) {
        (bool success, bytes memory data) = PRICE_ORACLE.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );
        if (!success || data.length < 32) {
            revert PriceOracleFailed();
        }
        return abi.decode(data, (uint256));
    }

    /**
     * @dev 请求紧急提取（精简版）
     */
    function requestEmergencyWithdraw(uint256 amount) external whenPaused {
        if (!emergencySigners[msg.sender]) revert NotAuthorized();
        if (amount > MAX_EMERGENCY_AMOUNT) revert AmountTooLarge();
        
        bytes32 requestId = keccak256(abi.encodePacked(amount, block.timestamp, msg.sender));
        // solhint-disable-line not-rely-on-time

        emergencyRequests[requestId] = EmergencyRequest({
            amount: uint128(amount),
            requestTime: uint64(block.timestamp), // solhint-disable-line not-rely-on-time
            executed: false,
            requester: msg.sender
        });

        emit EmergencyWithdrawRequested(requestId, amount, block.timestamp); // solhint-disable-line not-rely-on-time
    }

    /**
     * @dev 执行紧急提取（精简版）
     */
    function executeEmergencyWithdraw(bytes32 requestId) external {
        EmergencyRequest storage request = emergencyRequests[requestId];
        if (request.requestTime == 0) revert RequestNotFound();
        if (request.executed) revert AlreadyExecuted();
        if (block.timestamp < request.requestTime + EMERGENCY_DELAY) revert TimeLockActive();
        // solhint-disable-line not-rely-on-time
        if (!emergencySigners[msg.sender]) revert NotAuthorized();

        request.executed = true;
        if (!HAOX_TOKEN.transfer(owner(), request.amount)) {
            revert TransferFailed();
        }

        emit EmergencyWithdrawExecuted(requestId, request.amount);
    }

    /**
     * @dev 获取解锁进度（精简版）
     */
    function getUnlockProgress() external view returns (
        uint256 currentPrice,
        uint256 nextRound,
        uint256 targetPrice,
        bool conditionMet,
        uint256 timeRemaining
    ) {
        currentPrice = _getCurrentPrice();
        nextRound = currentRound;
        
        if (nextRound <= TOTAL_ROUNDS) {
            Round memory round = rounds[nextRound];
            targetPrice = round.triggerPrice;
            conditionMet = round.priceConditionMet;
            
            if (conditionMet && round.priceReachedTime > 0) {
                uint256 elapsed = block.timestamp - round.priceReachedTime; // solhint-disable-line not-rely-on-time
                timeRemaining = elapsed >= PRICE_MAINTAIN_DURATION ? 0 : PRICE_MAINTAIN_DURATION - elapsed;
            }
        }
    }

    /**
     * @dev 获取轮次信息
     */
    function getRoundInfo(uint256 roundNumber) external view returns (
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime,
        uint256 unlockTime
    ) {
        if (roundNumber == 0 || roundNumber > TOTAL_ROUNDS) revert InvalidRound();
        Round memory round = rounds[roundNumber];
        
        return (
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime,
            round.unlockTime
        );
    }

    /**
     * @dev 获取价格历史（精简版）
     */
    function getPriceHistory(uint256 roundNumber) external view returns (PriceCheck[10] memory) {
        return priceHistory[roundNumber];
    }

    /**
     * @dev 管理员功能
     */
    function addEmergencySigner(address signer) external onlyOwner {
        emergencySigners[signer] = true;
    }

    function removeEmergencySigner(address signer) external onlyOwner {
        emergencySigners[signer] = false;
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急暂停（无时间锁）
     */
    function emergencyPause() external {
        if (!emergencySigners[msg.sender]) revert NotAuthorized();
        _pause();
    }
}
