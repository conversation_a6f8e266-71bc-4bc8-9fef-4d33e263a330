[{"fullName": "contracts/HAOXPriceOracleV2.sol:AggregatorV3Interface", "displayName": "AggregatorV3Interface", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/Context.sol:Context", "displayName": "Context", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/token/ERC20/ERC20.sol:ERC20", "displayName": "ERC20", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/HAOXInvitationV2.sol:HAOXInvitationV2", "displayName": "HAOXInvitationV2", "deploySize": 6666, "previousDeploySize": 6666, "initSize": 7298, "previousInitSize": 7298}, {"fullName": "contracts/HAOXPresaleV2.sol:HAOXPresaleV2", "displayName": "HAOXPresaleV2", "deploySize": 4559, "previousDeploySize": 4559, "initSize": 5169, "previousInitSize": 5169}, {"fullName": "contracts/HAOXPriceAggregatorMinimal.sol:HAOXPriceAggregatorMinimal", "displayName": "HAOXPriceAggregatorMinimal", "deploySize": 5372, "previousDeploySize": 5372, "initSize": 5510, "previousInitSize": 5510}, {"fullName": "contracts/HAOXPriceOracleV2.sol:HAOXPriceOracleV2", "displayName": "HAOXPriceOracleV2", "deploySize": 4087, "previousDeploySize": 4087, "initSize": 6928, "previousInitSize": 6928}, {"fullName": "contracts/HAOXTokenV2.sol:HAOXTokenV2", "displayName": "HAOXTokenV2", "deploySize": 5666, "previousDeploySize": 5666, "initSize": 6722, "previousInitSize": 6722}, {"fullName": "contracts/HAOXVestingV2Minimal.sol:HAOXVestingV2Minimal", "displayName": "HAOXVestingV2Minimal", "deploySize": 5745, "previousDeploySize": 5745, "initSize": 6937, "previousInitSize": 6937}, {"fullName": "contracts/HAOXVestingV2Ultra.sol:HAOXVestingV2Ultra", "displayName": "HAOXVestingV2Ultra", "deploySize": 4304, "previousDeploySize": 4304, "initSize": 5605, "previousInitSize": 5605}, {"fullName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC1155Errors", "displayName": "IERC1155Errors", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", "displayName": "IERC20", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC20Errors", "displayName": "IERC20Errors", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol:IERC20Metadata", "displayName": "IERC20Metadata", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol:IERC721Errors", "displayName": "IERC721Errors", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "contracts/HAOXPriceOracleV2.sol:IPancakeSwapPair", "displayName": "IPancakeSwapPair", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/access/Ownable.sol:Ownable", "displayName": "Ownable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/Pausable.sol:Pausable", "displayName": "Pausable", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}, {"fullName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol:ReentrancyGuard", "displayName": "Reentrancy<PERSON><PERSON>", "deploySize": 0, "previousDeploySize": null, "initSize": 0, "previousInitSize": null}]