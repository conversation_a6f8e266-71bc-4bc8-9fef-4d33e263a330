{"contracts/HAOXInvitationV2.sol": {"l": {"103": 0, "104": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "122": 0, "123": 0, "126": 0, "127": 0, "136": 0, "137": 0, "140": 0, "141": 0, "144": 0, "145": 0, "146": 0, "147": 0, "150": 0, "151": 0, "152": 0, "153": 0, "156": 0, "163": 0, "164": 0, "166": 0, "167": 0, "168": 0, "174": 0, "175": 0, "176": 0, "179": 0, "181": 0, "188": 0, "189": 0, "191": 0, "192": 0, "193": 0, "195": 0, "196": 0, "197": 0, "198": 0, "200": 0, "202": 0, "203": 0, "210": 0, "211": 0, "218": 0, "219": 0, "222": 0, "225": 0, "226": 0, "227": 0, "228": 0, "229": 0, "230": 0, "236": 0, "237": 0, "239": 0, "240": 0, "241": 0, "242": 0, "243": 0, "246": 0, "247": 0, "256": 0, "257": 0, "263": 0, "264": 0, "265": 0, "268": 0, "283": 0, "284": 0, "299": 0, "306": 0, "313": 0, "320": 0, "327": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXInvitationV2.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "fnMap": {"1": {"name": "constructor", "line": 102, "loc": {"start": {"line": 102, "column": 4}, "end": {"line": 105, "column": 4}}}, "2": {"name": "recordInvitation", "line": 110, "loc": {"start": {"line": 110, "column": 4}, "end": {"line": 157, "column": 4}}}, "3": {"name": "claimRewards", "line": 162, "loc": {"start": {"line": 162, "column": 4}, "end": {"line": 182, "column": 4}}}, "4": {"name": "claimLeaderboardReward", "line": 187, "loc": {"start": {"line": 187, "column": 4}, "end": {"line": 204, "column": 4}}}, "5": {"name": "endPresale", "line": 209, "loc": {"start": {"line": 209, "column": 4}, "end": {"line": 212, "column": 4}}}, "6": {"name": "finalizeLeaderboard", "line": 217, "loc": {"start": {"line": 217, "column": 4}, "end": {"line": 248, "column": 4}}}, "7": {"name": "_getAllInviters", "line": 253, "loc": {"start": {"line": 253, "column": 4}, "end": {"line": 269, "column": 4}}}, "8": {"name": "getUserStats", "line": 274, "loc": {"start": {"line": 274, "column": 4}, "end": {"line": 293, "column": 4}}}, "9": {"name": "getUserInvitees", "line": 298, "loc": {"start": {"line": 298, "column": 4}, "end": {"line": 300, "column": 4}}}, "10": {"name": "getLeaderboard", "line": 305, "loc": {"start": {"line": 305, "column": 4}, "end": {"line": 307, "column": 4}}}, "11": {"name": "pause", "line": 312, "loc": {"start": {"line": 312, "column": 4}, "end": {"line": 314, "column": 4}}}, "12": {"name": "unpause", "line": 319, "loc": {"start": {"line": 319, "column": 4}, "end": {"line": 321, "column": 4}}}, "13": {"name": "emergencyWithdraw", "line": 326, "loc": {"start": {"line": 326, "column": 4}, "end": {"line": 328, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 65}}, "2": {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 91}}, "3": {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 46}}, "4": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 79}}, "5": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 56}}, "6": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 71}}, "7": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 40}}, "8": {"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 89}}, "9": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 52}}, "10": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 40}}, "11": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 4802}}, "12": {"start": {"line": 147, "column": 12}, "end": {"line": 147, "column": 65}}, "13": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 5066}}, "14": {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 67}}, "15": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 77}}, "16": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 55}}, "17": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 56}}, "18": {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 58}}, "19": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 53}}, "20": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 5770}}, "21": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 82}}, "22": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 55}}, "23": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 49}}, "24": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 65}}, "25": {"start": {"line": 191, "column": 8}, "end": {"line": 191, "column": 55}}, "26": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 68}}, "27": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 60}}, "28": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 48}}, "29": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 73}}, "30": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 58}}, "31": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 67}}, "32": {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 46}}, "33": {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 49}}, "34": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 58}}, "35": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 53}}, "36": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 7595}}, "37": {"start": {"line": 226, "column": 12}, "end": {"line": 226, "column": 7659}}, "38": {"start": {"line": 227, "column": 16}, "end": {"line": 227, "column": 7731}}, "39": {"start": {"line": 228, "column": 20}, "end": {"line": 228, "column": 46}}, "40": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 70}}, "41": {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 8152}}, "42": {"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 38}}, "43": {"start": {"line": 247, "column": 8}, "end": {"line": 247, "column": 46}}, "44": {"start": {"line": 256, "column": 8}, "end": {"line": 256, "column": 54}}, "45": {"start": {"line": 257, "column": 8}, "end": {"line": 257, "column": 25}}, "46": {"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 54}}, "47": {"start": {"line": 264, "column": 8}, "end": {"line": 264, "column": 8936}}, "48": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 21}}, "49": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 48}}, "50": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 9455}}, "51": {"start": {"line": 299, "column": 8}, "end": {"line": 299, "column": 31}}, "52": {"start": {"line": 306, "column": 8}, "end": {"line": 306, "column": 26}}, "53": {"start": {"line": 313, "column": 8}, "end": {"line": 313, "column": 15}}, "54": {"start": {"line": 320, "column": 8}, "end": {"line": 320, "column": 17}}, "55": {"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 70}}}, "branchMap": {"1": {"line": 103, "type": "if", "locations": [{"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 8}}, {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 8}}]}, "2": {"line": 115, "type": "if", "locations": [{"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 8}}, {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 8}}]}, "3": {"line": 116, "type": "if", "locations": [{"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 8}}, {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 8}}]}, "4": {"line": 117, "type": "if", "locations": [{"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 8}}, {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 8}}]}, "5": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}, {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}]}, "6": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 8}}, {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 8}}]}, "7": {"line": 144, "type": "if", "locations": [{"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 8}}, {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 8}}]}, "8": {"line": 150, "type": "if", "locations": [{"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}, {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}]}, "9": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 37}, "end": {"line": 162, "column": 37}}, {"start": {"line": 162, "column": 37}, "end": {"line": 162, "column": 37}}]}, "10": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 50}, "end": {"line": 162, "column": 50}}, {"start": {"line": 162, "column": 50}, "end": {"line": 162, "column": 50}}]}, "11": {"line": 166, "type": "if", "locations": [{"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}, {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}]}, "12": {"line": 167, "type": "if", "locations": [{"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 8}}, {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 8}}]}, "13": {"line": 168, "type": "if", "locations": [{"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}, {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}]}, "14": {"line": 179, "type": "if", "locations": [{"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}, {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}]}, "15": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 47}, "end": {"line": 187, "column": 47}}, {"start": {"line": 187, "column": 47}, "end": {"line": 187, "column": 47}}]}, "16": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 60}, "end": {"line": 187, "column": 60}}, {"start": {"line": 187, "column": 60}, "end": {"line": 187, "column": 60}}]}, "17": {"line": 188, "type": "if", "locations": [{"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}, {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}]}, "18": {"line": 189, "type": "if", "locations": [{"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}, {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}]}, "19": {"line": 192, "type": "if", "locations": [{"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 8}}, {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 8}}]}, "20": {"line": 193, "type": "if", "locations": [{"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 8}}, {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 8}}]}, "21": {"line": 200, "type": "if", "locations": [{"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}, {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}]}, "22": {"line": 209, "type": "if", "locations": [{"start": {"line": 209, "column": 35}, "end": {"line": 209, "column": 35}}, {"start": {"line": 209, "column": 35}, "end": {"line": 209, "column": 35}}]}, "23": {"line": 210, "type": "if", "locations": [{"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 8}}, {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 8}}]}, "24": {"line": 217, "type": "if", "locations": [{"start": {"line": 217, "column": 44}, "end": {"line": 217, "column": 44}}, {"start": {"line": 217, "column": 44}, "end": {"line": 217, "column": 44}}]}, "25": {"line": 218, "type": "if", "locations": [{"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 8}}, {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 8}}]}, "26": {"line": 219, "type": "if", "locations": [{"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}, {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}]}, "27": {"line": 227, "type": "if", "locations": [{"start": {"line": 227, "column": 16}, "end": {"line": 227, "column": 16}}, {"start": {"line": 227, "column": 16}, "end": {"line": 227, "column": 16}}]}, "28": {"line": 236, "type": "if", "locations": [{"start": {"line": 236, "column": 50}, "end": {"line": 236, "column": 51}}, {"start": {"line": 236, "column": 55}, "end": {"line": 236, "column": 69}}]}, "29": {"line": 312, "type": "if", "locations": [{"start": {"line": 312, "column": 30}, "end": {"line": 312, "column": 30}}, {"start": {"line": 312, "column": 30}, "end": {"line": 312, "column": 30}}]}, "30": {"line": 319, "type": "if", "locations": [{"start": {"line": 319, "column": 32}, "end": {"line": 319, "column": 32}}, {"start": {"line": 319, "column": 32}, "end": {"line": 319, "column": 32}}]}, "31": {"line": 326, "type": "if", "locations": [{"start": {"line": 326, "column": 56}, "end": {"line": 326, "column": 56}}, {"start": {"line": 326, "column": 56}, "end": {"line": 326, "column": 56}}]}, "32": {"line": 327, "type": "if", "locations": [{"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 8}}, {"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 8}}]}}}, "contracts/HAOXPresaleV2.sol": {"l": {"62": 0, "63": 0, "64": 0, "66": 0, "67": 0, "68": 0, "70": 0, "71": 0, "72": 0, "83": 0, "85": 0, "86": 0, "88": 0, "89": 0, "90": 0, "93": 0, "100": 0, "102": 0, "103": 0, "106": 0, "107": 0, "108": 0, "110": 0, "126": 0, "127": 0, "128": 0, "129": 0, "131": 0, "132": 0, "133": 0, "135": 0, "137": 0, "138": 0, "141": 0, "142": 0, "143": 0, "146": 0, "147": 0, "151": 0, "154": 0, "155": 0, "156": 0, "163": 0, "164": 0, "165": 0, "166": 0, "170": 0, "175": 0, "178": 0, "181": 0, "182": 0, "188": 0, "191": 0, "192": 0, "195": 0, "196": 0, "203": 0, "213": 0, "214": 0, "216": 0, "221": 0, "233": 0, "234": 0, "236": 0, "237": 0, "239": 0, "240": 0, "243": 0, "245": 0, "247": 0, "248": 0, "253": 0, "254": 0, "261": 0, "268": 0, "269": 0, "277": 0, "284": 0, "291": 0, "292": 0, "306": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPresaleV2.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "fnMap": {"1": {"name": "constructor", "line": 61, "loc": {"start": {"line": 57, "column": 4}, "end": {"line": 73, "column": 4}}}, "2": {"name": "initializeRates", "line": 82, "loc": {"start": {"line": 82, "column": 4}, "end": {"line": 94, "column": 4}}}, "3": {"name": "getStageRate", "line": 99, "loc": {"start": {"line": 99, "column": 4}, "end": {"line": 112, "column": 4}}}, "4": {"name": "calculateCrossStageTokens", "line": 121, "loc": {"start": {"line": 121, "column": 4}, "end": {"line": 157, "column": 4}}}, "5": {"name": "buyTokens", "line": 162, "loc": {"start": {"line": 162, "column": 4}, "end": {"line": 223, "column": 4}}}, "6": {"name": "_updateStageProgress", "line": 228, "loc": {"start": {"line": 228, "column": 4}, "end": {"line": 255, "column": 4}}}, "7": {"name": "getCurrentRate", "line": 260, "loc": {"start": {"line": 260, "column": 4}, "end": {"line": 262, "column": 4}}}, "8": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 267, "loc": {"start": {"line": 267, "column": 4}, "end": {"line": 271, "column": 4}}}, "9": {"name": "pause", "line": 276, "loc": {"start": {"line": 276, "column": 4}, "end": {"line": 278, "column": 4}}}, "10": {"name": "unpause", "line": 283, "loc": {"start": {"line": 283, "column": 4}, "end": {"line": 285, "column": 4}}}, "11": {"name": "withdrawBNB", "line": 290, "loc": {"start": {"line": 290, "column": 4}, "end": {"line": 293, "column": 4}}}, "12": {"name": "getPresaleStatus", "line": 298, "loc": {"start": {"line": 298, "column": 4}, "end": {"line": 314, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 65}}, "2": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 79}}, "3": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 68}}, "4": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 63}}, "5": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 35}}, "6": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 2940}}, "7": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 53}}, "8": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 3311}}, "9": {"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 37}}, "10": {"start": {"line": 106, "column": 12}, "end": {"line": 106, "column": 39}}, "11": {"start": {"line": 107, "column": 12}, "end": {"line": 107, "column": 3472}}, "12": {"start": {"line": 110, "column": 12}, "end": {"line": 110, "column": 23}}, "13": {"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 40}}, "14": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 31}}, "15": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 36}}, "16": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 68}}, "17": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 4160}}, "18": {"start": {"line": 132, "column": 12}, "end": {"line": 132, "column": 51}}, "19": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 70}}, "20": {"start": {"line": 135, "column": 12}, "end": {"line": 135, "column": 4365}}, "21": {"start": {"line": 141, "column": 16}, "end": {"line": 141, "column": 83}}, "22": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 66}}, "23": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 51}}, "24": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 56}}, "25": {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 71}}, "26": {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 5462}}, "27": {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 5598}}, "28": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 41}}, "29": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 5833}}, "30": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 55}}, "31": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 5978}}, "32": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 61}}, "33": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 6349}}, "34": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 6419}}, "35": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 6603}}, "36": {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 6802}}, "37": {"start": {"line": 216, "column": 12}, "end": {"line": 216, "column": 6958}}, "38": {"start": {"line": 221, "column": 12}, "end": {"line": 221, "column": 62}}, "39": {"start": {"line": 233, "column": 8}, "end": {"line": 233, "column": 40}}, "40": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 45}}, "41": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 7453}}, "42": {"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 7530}}, "43": {"start": {"line": 245, "column": 16}, "end": {"line": 245, "column": 100}}, "44": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 41}}, "45": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 8476}}, "46": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 15}}, "47": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 17}}, "48": {"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 54}}, "49": {"start": {"line": 292, "column": 8}, "end": {"line": 292, "column": 55}}, "50": {"start": {"line": 306, "column": 8}, "end": {"line": 306, "column": 9309}}}, "branchMap": {"1": {"line": 62, "type": "if", "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}, {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}]}, "2": {"line": 63, "type": "if", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 8}}, {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 8}}]}, "3": {"line": 64, "type": "if", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}, {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}]}, "4": {"line": 82, "type": "if", "locations": [{"start": {"line": 82, "column": 40}, "end": {"line": 82, "column": 40}}, {"start": {"line": 82, "column": 40}, "end": {"line": 82, "column": 40}}]}, "5": {"line": 83, "type": "if", "locations": [{"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 8}}, {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 8}}]}, "6": {"line": 100, "type": "if", "locations": [{"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 8}}, {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 8}}]}, "7": {"line": 102, "type": "if", "locations": [{"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 8}}, {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 8}}]}, "8": {"line": 135, "type": "if", "locations": [{"start": {"line": 135, "column": 12}, "end": {"line": 135, "column": 12}}, {"start": {"line": 135, "column": 12}, "end": {"line": 135, "column": 12}}]}, "9": {"line": 151, "type": "if", "locations": [{"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 8}}, {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 8}}]}, "10": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 57}, "end": {"line": 162, "column": 57}}, {"start": {"line": 162, "column": 57}, "end": {"line": 162, "column": 57}}]}, "11": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 70}, "end": {"line": 162, "column": 70}}, {"start": {"line": 162, "column": 70}, "end": {"line": 162, "column": 70}}]}, "12": {"line": 163, "type": "if", "locations": [{"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 8}}, {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 8}}]}, "13": {"line": 164, "type": "if", "locations": [{"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 8}}, {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 8}}]}, "14": {"line": 165, "type": "if", "locations": [{"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}, {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}]}, "15": {"line": 166, "type": "if", "locations": [{"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}, {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}]}, "16": {"line": 170, "type": "if", "locations": [{"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 8}}, {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 8}}]}, "17": {"line": 181, "type": "if", "locations": [{"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 8}}, {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 8}}]}, "18": {"line": 182, "type": "if", "locations": [{"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}, {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}]}, "19": {"line": 195, "type": "if", "locations": [{"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}, {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}]}, "20": {"line": 213, "type": "if", "locations": [{"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}, {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}]}, "21": {"line": 213, "type": "cond-expr", "locations": [{"start": {"line": 213, "column": 12}, "end": {"line": 213, "column": 39}}, {"start": {"line": 213, "column": 44}, "end": {"line": 213, "column": 82}}]}, "22": {"line": 237, "type": "if", "locations": [{"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 12}}, {"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 12}}]}, "23": {"line": 267, "type": "if", "locations": [{"start": {"line": 267, "column": 63}, "end": {"line": 267, "column": 63}}, {"start": {"line": 267, "column": 63}, "end": {"line": 267, "column": 63}}]}, "24": {"line": 276, "type": "if", "locations": [{"start": {"line": 276, "column": 30}, "end": {"line": 276, "column": 30}}, {"start": {"line": 276, "column": 30}, "end": {"line": 276, "column": 30}}]}, "25": {"line": 283, "type": "if", "locations": [{"start": {"line": 283, "column": 32}, "end": {"line": 283, "column": 32}}, {"start": {"line": 283, "column": 32}, "end": {"line": 283, "column": 32}}]}, "26": {"line": 290, "type": "if", "locations": [{"start": {"line": 290, "column": 36}, "end": {"line": 290, "column": 36}}, {"start": {"line": 290, "column": 36}, "end": {"line": 290, "column": 36}}]}, "27": {"line": 291, "type": "if", "locations": [{"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 8}}, {"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 8}}]}}}, "contracts/HAOXPriceAggregatorMinimal.sol": {"l": {"53": 0, "60": 0, "61": 0, "62": 0, "65": 0, "66": 0, "69": 0, "76": 0, "77": 0, "84": 0, "85": 0, "87": 0, "88": 0, "90": 0, "97": 0, "98": 0, "102": 0, "105": 0, "106": 0, "111": 0, "118": 0, "125": 0, "127": 0, "128": 0, "129": 0, "132": 0, "133": 0, "136": 0, "137": 0, "140": 0, "142": 0, "143": 0, "144": 0, "146": 0, "149": 0, "151": 0, "154": 0, "156": 0, "157": 0, "158": 0, "160": 0, "167": 0, "169": 0, "173": 0, "174": 0, "181": 0, "183": 0, "187": 0, "188": 0, "189": 0, "191": 0, "192": 0, "200": 0, "202": 0, "206": 0, "213": 0, "215": 0, "216": 0, "217": 0, "219": 0, "226": 0, "228": 0, "229": 0, "230": 0, "232": 0, "234": 0, "235": 0, "236": 0, "238": 0, "251": 0, "252": 0, "253": 0, "256": 0, "274": 0, "275": 0, "277": 0, "284": 0, "286": 0, "287": 0, "288": 0, "289": 0, "298": 0, "302": 0, "310": 0, "311": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPriceAggregatorMinimal.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "fnMap": {"1": {"name": "constructor", "line": 52, "loc": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 4}}}, "2": {"name": "addPriceSource", "line": 59, "loc": {"start": {"line": 59, "column": 4}, "end": {"line": 78, "column": 4}}}, "3": {"name": "updatePriceSource", "line": 83, "loc": {"start": {"line": 83, "column": 4}, "end": {"line": 91, "column": 4}}}, "4": {"name": "getLatestPrice", "line": 96, "loc": {"start": {"line": 96, "column": 4}, "end": {"line": 112, "column": 4}}}, "5": {"name": "getLastUpdateTime", "line": 117, "loc": {"start": {"line": 117, "column": 4}, "end": {"line": 119, "column": 4}}}, "6": {"name": "updateAggregatedPrice", "line": 124, "loc": {"start": {"line": 124, "column": 4}, "end": {"line": 161, "column": 4}}}, "7": {"name": "_getPriceFromSource", "line": 166, "loc": {"start": {"line": 166, "column": 4}, "end": {"line": 175, "column": 4}}}, "8": {"name": "_tryGetPriceFromSource", "line": 180, "loc": {"start": {"line": 180, "column": 4}, "end": {"line": 194, "column": 4}}}, "9": {"name": "_validatePriceDeviation", "line": 199, "loc": {"start": {"line": 199, "column": 4}, "end": {"line": 207, "column": 4}}}, "10": {"name": "activateEmergencyMode", "line": 212, "loc": {"start": {"line": 212, "column": 4}, "end": {"line": 220, "column": 4}}}, "11": {"name": "deactivateEmergencyMode", "line": 225, "loc": {"start": {"line": 225, "column": 4}, "end": {"line": 239, "column": 4}}}, "12": {"name": "getAggregatorStatus", "line": 244, "loc": {"start": {"line": 244, "column": 4}, "end": {"line": 263, "column": 4}}}, "13": {"name": "getPriceSource", "line": 268, "loc": {"start": {"line": 268, "column": 4}, "end": {"line": 278, "column": 4}}}, "14": {"name": "batchUpdateSources", "line": 283, "loc": {"start": {"line": 283, "column": 4}, "end": {"line": 292, "column": 4}}}, "15": {"name": "pause", "line": 297, "loc": {"start": {"line": 297, "column": 4}, "end": {"line": 299, "column": 4}}}, "16": {"name": "unpause", "line": 301, "loc": {"start": {"line": 301, "column": 4}, "end": {"line": 303, "column": 4}}}, "17": {"name": "emergencyPause", "line": 308, "loc": {"start": {"line": 308, "column": 4}, "end": {"line": 312, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 54}}, "2": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 61}}, "3": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 61}}, "4": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 1907}}, "5": {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 69}}, "6": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 58}}, "7": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 56}}, "8": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 61}}, "9": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 57}}, "10": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 2850}}, "11": {"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 2887}}, "12": {"start": {"line": 102, "column": 12}, "end": {"line": 102, "column": 33}}, "13": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 56}}, "14": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 3160}}, "15": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 26}}, "16": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 71}}, "17": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 55}}, "18": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 31}}, "19": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 31}}, "20": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 30}}, "21": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 3811}}, "22": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 49}}, "23": {"start": {"line": 136, "column": 12}, "end": {"line": 136, "column": 69}}, "24": {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 34}}, "25": {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 52}}, "26": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 76}}, "27": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 59}}, "28": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 47}}, "29": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 76}}, "30": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 54}}, "31": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 4984}}, "32": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 66}}, "33": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 42}}, "34": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 54}}, "35": {"start": {"line": 183, "column": 8}, "end": {"line": 183, "column": 5443}}, "36": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 5577}}, "37": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 36}}, "38": {"start": {"line": 200, "column": 30}, "end": {"line": 200, "column": 36}}, "39": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 5998}}, "40": {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 80}}, "41": {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 42}}, "42": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 59}}, "43": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 54}}, "44": {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 29}}, "45": {"start": {"line": 229, "column": 8}, "end": {"line": 229, "column": 6765}}, "46": {"start": {"line": 230, "column": 12}, "end": {"line": 230, "column": 53}}, "47": {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 75}}, "48": {"start": {"line": 238, "column": 8}, "end": {"line": 238, "column": 39}}, "49": {"start": {"line": 251, "column": 8}, "end": {"line": 251, "column": 24}}, "50": {"start": {"line": 252, "column": 8}, "end": {"line": 252, "column": 7393}}, "51": {"start": {"line": 253, "column": 12}, "end": {"line": 253, "column": 48}}, "52": {"start": {"line": 256, "column": 8}, "end": {"line": 256, "column": 7513}}, "53": {"start": {"line": 274, "column": 8}, "end": {"line": 274, "column": 56}}, "54": {"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 58}}, "55": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 79}}, "56": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 74}}, "57": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 8327}}, "58": {"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": 8392}}, "59": {"start": {"line": 289, "column": 16}, "end": {"line": 289, "column": 105}}, "60": {"start": {"line": 298, "column": 8}, "end": {"line": 298, "column": 15}}, "61": {"start": {"line": 302, "column": 8}, "end": {"line": 302, "column": 17}}, "62": {"start": {"line": 310, "column": 8}, "end": {"line": 310, "column": 55}}, "63": {"start": {"line": 311, "column": 8}, "end": {"line": 311, "column": 15}}}, "branchMap": {"1": {"line": 59, "type": "if", "locations": [{"start": {"line": 59, "column": 67}, "end": {"line": 59, "column": 67}}, {"start": {"line": 59, "column": 67}, "end": {"line": 59, "column": 67}}]}, "2": {"line": 60, "type": "if", "locations": [{"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 8}}, {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 8}}]}, "3": {"line": 61, "type": "if", "locations": [{"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 8}}, {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 8}}]}, "4": {"line": 62, "type": "if", "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}, {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}]}, "5": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 12}}, {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 12}}]}, "6": {"line": 83, "type": "if", "locations": [{"start": {"line": 83, "column": 83}, "end": {"line": 83, "column": 83}}, {"start": {"line": 83, "column": 83}, "end": {"line": 83, "column": 83}}]}, "7": {"line": 84, "type": "if", "locations": [{"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 8}}, {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 8}}]}, "8": {"line": 85, "type": "if", "locations": [{"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 8}}, {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 8}}]}, "9": {"line": 97, "type": "if", "locations": [{"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}, {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}]}, "10": {"line": 98, "type": "if", "locations": [{"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 12}}, {"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 12}}]}, "11": {"line": 105, "type": "if", "locations": [{"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}, {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}]}, "12": {"line": 106, "type": "if", "locations": [{"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 8}}, {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 8}}]}, "13": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 31}, "end": {"line": 118, "column": 53}}, {"start": {"line": 118, "column": 57}, "end": {"line": 118, "column": 70}}]}, "14": {"line": 124, "type": "if", "locations": [{"start": {"line": 124, "column": 46}, "end": {"line": 124, "column": 46}}, {"start": {"line": 124, "column": 46}, "end": {"line": 124, "column": 46}}]}, "15": {"line": 125, "type": "if", "locations": [{"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}, {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}]}, "16": {"line": 133, "type": "if", "locations": [{"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 12}}, {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 12}}]}, "17": {"line": 137, "type": "if", "locations": [{"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}, {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}]}, "18": {"line": 140, "type": "if", "locations": [{"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 12}}, {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 12}}]}, "19": {"line": 140, "type": "cond-expr", "locations": [{"start": {"line": 140, "column": 16}, "end": {"line": 140, "column": 25}}, {"start": {"line": 140, "column": 30}, "end": {"line": 140, "column": 41}}]}, "20": {"line": 149, "type": "if", "locations": [{"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 8}}, {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 8}}]}, "21": {"line": 173, "type": "if", "locations": [{"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 8}}, {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 8}}]}, "22": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}, {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}]}, "23": {"line": 200, "type": "if", "locations": [{"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}, {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}]}, "24": {"line": 203, "type": "if", "locations": [{"start": {"line": 203, "column": 14}, "end": {"line": 203, "column": 59}}, {"start": {"line": 204, "column": 14}, "end": {"line": 204, "column": 59}}]}, "25": {"line": 206, "type": "if", "locations": [{"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 8}}, {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 8}}]}, "26": {"line": 212, "type": "if", "locations": [{"start": {"line": 212, "column": 59}, "end": {"line": 212, "column": 59}}, {"start": {"line": 212, "column": 59}, "end": {"line": 212, "column": 59}}]}, "27": {"line": 213, "type": "if", "locations": [{"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}, {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}]}, "28": {"line": 225, "type": "if", "locations": [{"start": {"line": 225, "column": 48}, "end": {"line": 225, "column": 48}}, {"start": {"line": 225, "column": 48}, "end": {"line": 225, "column": 48}}]}, "29": {"line": 226, "type": "if", "locations": [{"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 8}}, {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 8}}]}, "30": {"line": 230, "type": "if", "locations": [{"start": {"line": 230, "column": 12}, "end": {"line": 230, "column": 12}}, {"start": {"line": 230, "column": 12}, "end": {"line": 230, "column": 12}}]}, "31": {"line": 232, "type": "if", "locations": [{"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 8}}, {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 8}}]}, "32": {"line": 253, "type": "if", "locations": [{"start": {"line": 253, "column": 12}, "end": {"line": 253, "column": 12}}, {"start": {"line": 253, "column": 12}, "end": {"line": 253, "column": 12}}]}, "33": {"line": 274, "type": "if", "locations": [{"start": {"line": 274, "column": 8}, "end": {"line": 274, "column": 8}}, {"start": {"line": 274, "column": 8}, "end": {"line": 274, "column": 8}}]}, "34": {"line": 283, "type": "if", "locations": [{"start": {"line": 283, "column": 99}, "end": {"line": 283, "column": 99}}, {"start": {"line": 283, "column": 99}, "end": {"line": 283, "column": 99}}]}, "35": {"line": 284, "type": "if", "locations": [{"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 8}}, {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 8}}]}, "36": {"line": 287, "type": "if", "locations": [{"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": 12}}, {"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": 12}}]}, "37": {"line": 297, "type": "if", "locations": [{"start": {"line": 297, "column": 30}, "end": {"line": 297, "column": 30}}, {"start": {"line": 297, "column": 30}, "end": {"line": 297, "column": 30}}]}, "38": {"line": 301, "type": "if", "locations": [{"start": {"line": 301, "column": 32}, "end": {"line": 301, "column": 32}}, {"start": {"line": 301, "column": 32}, "end": {"line": 301, "column": 32}}]}, "39": {"line": 310, "type": "if", "locations": [{"start": {"line": 310, "column": 8}, "end": {"line": 310, "column": 8}}, {"start": {"line": 310, "column": 8}, "end": {"line": 310, "column": 8}}]}}}, "contracts/HAOXPriceAggregatorV2.sol": {"l": {"99": 0, "100": 0, "101": 0, "109": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "128": 0, "129": 0, "132": 0, "143": 0, "144": 0, "156": 0, "157": 0, "159": 0, "160": 0, "161": 0, "162": 0, "165": 0, "166": 0, "169": 0, "177": 0, "178": 0, "180": 0, "181": 0, "188": 0, "189": 0, "193": 0, "196": 0, "197": 0, "202": 0, "209": 0, "210": 0, "212": 0, "219": 0, "221": 0, "223": 0, "226": 0, "234": 0, "236": 0, "247": 0, "248": 0, "249": 0, "250": 0, "251": 0, "254": 0, "255": 0, "256": 0, "259": 0, "260": 0, "261": 0, "262": 0, "265": 0, "266": 0, "267": 0, "271": 0, "272": 0, "273": 0, "277": 0, "278": 0, "279": 0, "282": 0, "283": 0, "284": 0, "286": 0, "287": 0, "290": 0, "291": 0, "294": 0, "295": 0, "299": 0, "301": 0, "304": 0, "307": 0, "309": 0, "316": 0, "319": 0, "323": 0, "324": 0, "326": 0, "333": 0, "336": 0, "340": 0, "341": 0, "345": 0, "352": 0, "354": 0, "358": 0, "359": 0, "360": 0, "362": 0, "363": 0, "371": 0, "373": 0, "377": 0, "378": 0, "379": 0, "381": 0, "382": 0, "395": 0, "396": 0, "400": 0, "401": 0, "404": 0, "405": 0, "416": 0, "417": 0, "420": 0, "421": 0, "423": 0, "424": 0, "431": 0, "432": 0, "434": 0, "437": 0, "438": 0, "439": 0, "447": 0, "449": 0, "450": 0, "452": 0, "454": 0, "462": 0, "464": 0, "465": 0, "466": 0, "468": 0, "475": 0, "476": 0, "478": 0, "479": 0, "480": 0, "482": 0, "489": 0, "490": 0, "491": 0, "492": 0, "495": 0, "509": 0, "510": 0, "511": 0, "512": 0, "513": 0, "514": 0, "516": 0, "517": 0, "518": 0, "519": 0, "520": 0, "521": 0, "522": 0, "523": 0, "531": 0, "532": 0, "534": 0, "535": 0, "536": 0, "539": 0, "553": 0, "567": 0, "574": 0, "584": 0, "586": 0, "587": 0, "588": 0, "591": 0, "592": 0, "595": 0, "609": 0, "612": 0, "613": 0, "614": 0, "637": 0, "638": 0, "641": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPriceAggregatorV2.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0], "47": [0, 0], "48": [0, 0], "49": [0, 0], "50": [0, 0], "51": [0, 0], "52": [0, 0], "53": [0, 0], "54": [0, 0], "55": [0, 0], "56": [0, 0], "57": [0, 0], "58": [0, 0], "59": [0, 0], "60": [0, 0], "61": [0, 0], "62": [0, 0], "63": [0, 0], "64": [0, 0], "65": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "fnMap": {"1": {"name": "validSourceId", "line": 98, "loc": {"start": {"line": 98, "column": 4}, "end": {"line": 102, "column": 4}}}, "2": {"name": "constructor", "line": 107, "loc": {"start": {"line": 107, "column": 4}, "end": {"line": 110, "column": 4}}}, "3": {"name": "addPriceSource", "line": 120, "loc": {"start": {"line": 115, "column": 4}, "end": {"line": 145, "column": 4}}}, "4": {"name": "updatePriceSource", "line": 155, "loc": {"start": {"line": 150, "column": 4}, "end": {"line": 170, "column": 4}}}, "5": {"name": "removePriceSource", "line": 175, "loc": {"start": {"line": 175, "column": 4}, "end": {"line": 182, "column": 4}}}, "6": {"name": "getLatestPrice", "line": 187, "loc": {"start": {"line": 187, "column": 4}, "end": {"line": 203, "column": 4}}}, "7": {"name": "getLastUpdateTime", "line": 208, "loc": {"start": {"line": 208, "column": 4}, "end": {"line": 213, "column": 4}}}, "8": {"name": "updateAggregatedPrice", "line": 218, "loc": {"start": {"line": 218, "column": 4}, "end": {"line": 237, "column": 4}}}, "9": {"name": "_calculateAggregatedPrice", "line": 242, "loc": {"start": {"line": 242, "column": 4}, "end": {"line": 310, "column": 4}}}, "10": {"name": "_getPriceFromSource", "line": 315, "loc": {"start": {"line": 315, "column": 4}, "end": {"line": 327, "column": 4}}}, "11": {"name": "_getUpdateTimeFromSource", "line": 332, "loc": {"start": {"line": 332, "column": 4}, "end": {"line": 346, "column": 4}}}, "12": {"name": "_tryGetPriceFromSource", "line": 351, "loc": {"start": {"line": 351, "column": 4}, "end": {"line": 365, "column": 4}}}, "13": {"name": "_tryGetUpdateTimeFromSource", "line": 370, "loc": {"start": {"line": 370, "column": 4}, "end": {"line": 384, "column": 4}}}, "14": {"name": "_validatePriceDeviation", "line": 389, "loc": {"start": {"line": 389, "column": 4}, "end": {"line": 409, "column": 4}}}, "15": {"name": "_calculateConfidence", "line": 414, "loc": {"start": {"line": 414, "column": 4}, "end": {"line": 425, "column": 4}}}, "16": {"name": "_handleSourceFailure", "line": 430, "loc": {"start": {"line": 430, "column": 4}, "end": {"line": 441, "column": 4}}}, "17": {"name": "_addToHistory", "line": 446, "loc": {"start": {"line": 446, "column": 4}, "end": {"line": 456, "column": 4}}}, "18": {"name": "activateEmergencyMode", "line": 461, "loc": {"start": {"line": 461, "column": 4}, "end": {"line": 469, "column": 4}}}, "19": {"name": "deactivateEmergencyMode", "line": 474, "loc": {"start": {"line": 474, "column": 4}, "end": {"line": 483, "column": 4}}}, "20": {"name": "getActiveSourceCount", "line": 488, "loc": {"start": {"line": 488, "column": 4}, "end": {"line": 496, "column": 4}}}, "21": {"name": "getAllSources", "line": 501, "loc": {"start": {"line": 501, "column": 4}, "end": {"line": 525, "column": 4}}}, "22": {"name": "getPriceHistory", "line": 530, "loc": {"start": {"line": 530, "column": 4}, "end": {"line": 540, "column": 4}}}, "23": {"name": "getAggregatorStatus", "line": 545, "loc": {"start": {"line": 545, "column": 4}, "end": {"line": 561, "column": 4}}}, "24": {"name": "pause", "line": 566, "loc": {"start": {"line": 566, "column": 4}, "end": {"line": 568, "column": 4}}}, "25": {"name": "unpause", "line": 573, "loc": {"start": {"line": 573, "column": 4}, "end": {"line": 575, "column": 4}}}, "26": {"name": "batchUpdateSources", "line": 583, "loc": {"start": {"line": 580, "column": 4}, "end": {"line": 603, "column": 4}}}, "27": {"name": "resetSourceFailures", "line": 608, "loc": {"start": {"line": 608, "column": 4}, "end": {"line": 621, "column": 4}}}, "28": {"name": "getSourceDetails", "line": 626, "loc": {"start": {"line": 626, "column": 4}, "end": {"line": 652, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 59}}, "2": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 80}}, "3": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 62}}, "4": {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 61}}, "5": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 55}}, "6": {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 61}}, "7": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 76}}, "8": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 3280}}, "9": {"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 77}}, "10": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 64}}, "11": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 61}}, "12": {"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 76}}, "13": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 59}}, "14": {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 4394}}, "15": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 70}}, "16": {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 52}}, "17": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 88}}, "18": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 41}}, "19": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 5045}}, "20": {"start": {"line": 189, "column": 12}, "end": {"line": 189, "column": 5082}}, "21": {"start": {"line": 193, "column": 12}, "end": {"line": 193, "column": 33}}, "22": {"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 63}}, "23": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 5366}}, "24": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 32}}, "25": {"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 5660}}, "26": {"start": {"line": 210, "column": 12}, "end": {"line": 210, "column": 42}}, "27": {"start": {"line": 212, "column": 8}, "end": {"line": 212, "column": 36}}, "28": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 55}}, "29": {"start": {"line": 221, "column": 8}, "end": {"line": 221, "column": 105}}, "30": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 82}}, "31": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 33}}, "32": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 88}}, "33": {"start": {"line": 247, "column": 8}, "end": {"line": 247, "column": 31}}, "34": {"start": {"line": 248, "column": 8}, "end": {"line": 248, "column": 31}}, "35": {"start": {"line": 249, "column": 8}, "end": {"line": 249, "column": 60}}, "36": {"start": {"line": 250, "column": 8}, "end": {"line": 250, "column": 61}}, "37": {"start": {"line": 254, "column": 8}, "end": {"line": 254, "column": 6998}}, "38": {"start": {"line": 255, "column": 12}, "end": {"line": 255, "column": 56}}, "39": {"start": {"line": 256, "column": 12}, "end": {"line": 256, "column": 40}}, "40": {"start": {"line": 259, "column": 12}, "end": {"line": 259, "column": 74}}, "41": {"start": {"line": 260, "column": 12}, "end": {"line": 260, "column": 7268}}, "42": {"start": {"line": 261, "column": 16}, "end": {"line": 261, "column": 60}}, "43": {"start": {"line": 265, "column": 12}, "end": {"line": 265, "column": 83}}, "44": {"start": {"line": 266, "column": 12}, "end": {"line": 266, "column": 7490}}, "45": {"start": {"line": 271, "column": 12}, "end": {"line": 271, "column": 7622}}, "46": {"start": {"line": 272, "column": 16}, "end": {"line": 272, "column": 57}}, "47": {"start": {"line": 277, "column": 12}, "end": {"line": 277, "column": 7832}}, "48": {"start": {"line": 278, "column": 16}, "end": {"line": 278, "column": 61}}, "49": {"start": {"line": 294, "column": 12}, "end": {"line": 294, "column": 8348}}, "50": {"start": {"line": 299, "column": 8}, "end": {"line": 299, "column": 82}}, "51": {"start": {"line": 304, "column": 8}, "end": {"line": 304, "column": 78}}, "52": {"start": {"line": 309, "column": 8}, "end": {"line": 309, "column": 58}}, "53": {"start": {"line": 316, "column": 8}, "end": {"line": 316, "column": 58}}, "54": {"start": {"line": 319, "column": 8}, "end": {"line": 319, "column": 9107}}, "55": {"start": {"line": 323, "column": 8}, "end": {"line": 323, "column": 45}}, "56": {"start": {"line": 324, "column": 8}, "end": {"line": 324, "column": 53}}, "57": {"start": {"line": 326, "column": 8}, "end": {"line": 326, "column": 42}}, "58": {"start": {"line": 333, "column": 8}, "end": {"line": 333, "column": 58}}, "59": {"start": {"line": 336, "column": 8}, "end": {"line": 336, "column": 9627}}, "60": {"start": {"line": 340, "column": 8}, "end": {"line": 340, "column": 9767}}, "61": {"start": {"line": 341, "column": 12}, "end": {"line": 341, "column": 46}}, "62": {"start": {"line": 345, "column": 8}, "end": {"line": 345, "column": 30}}, "63": {"start": {"line": 352, "column": 8}, "end": {"line": 352, "column": 58}}, "64": {"start": {"line": 354, "column": 8}, "end": {"line": 354, "column": 10153}}, "65": {"start": {"line": 358, "column": 8}, "end": {"line": 358, "column": 10294}}, "66": {"start": {"line": 371, "column": 8}, "end": {"line": 371, "column": 58}}, "67": {"start": {"line": 373, "column": 8}, "end": {"line": 373, "column": 10732}}, "68": {"start": {"line": 377, "column": 8}, "end": {"line": 377, "column": 10876}}, "69": {"start": {"line": 395, "column": 8}, "end": {"line": 395, "column": 11323}}, "70": {"start": {"line": 396, "column": 12}, "end": {"line": 396, "column": 11438}}, "71": {"start": {"line": 400, "column": 12}, "end": {"line": 400, "column": 11603}}, "72": {"start": {"line": 401, "column": 16}, "end": {"line": 401, "column": 85}}, "73": {"start": {"line": 404, "column": 16}, "end": {"line": 404, "column": 11806}}, "74": {"start": {"line": 405, "column": 20}, "end": {"line": 405, "column": 70}}, "75": {"start": {"line": 416, "column": 8}, "end": {"line": 416, "column": 54}}, "76": {"start": {"line": 417, "column": 8}, "end": {"line": 417, "column": 52}}, "77": {"start": {"line": 420, "column": 8}, "end": {"line": 420, "column": 90}}, "78": {"start": {"line": 421, "column": 8}, "end": {"line": 421, "column": 46}}, "79": {"start": {"line": 423, "column": 8}, "end": {"line": 423, "column": 57}}, "80": {"start": {"line": 424, "column": 8}, "end": {"line": 424, "column": 50}}, "81": {"start": {"line": 431, "column": 8}, "end": {"line": 431, "column": 59}}, "82": {"start": {"line": 434, "column": 8}, "end": {"line": 434, "column": 69}}, "83": {"start": {"line": 437, "column": 8}, "end": {"line": 437, "column": 12888}}, "84": {"start": {"line": 439, "column": 12}, "end": {"line": 439, "column": 87}}, "85": {"start": {"line": 447, "column": 8}, "end": {"line": 447, "column": 13188}}, "86": {"start": {"line": 449, "column": 12}, "end": {"line": 449, "column": 13274}}, "87": {"start": {"line": 454, "column": 12}, "end": {"line": 454, "column": 39}}, "88": {"start": {"line": 462, "column": 8}, "end": {"line": 462, "column": 52}}, "89": {"start": {"line": 468, "column": 8}, "end": {"line": 468, "column": 59}}, "90": {"start": {"line": 475, "column": 8}, "end": {"line": 475, "column": 58}}, "91": {"start": {"line": 476, "column": 8}, "end": {"line": 476, "column": 93}}, "92": {"start": {"line": 482, "column": 8}, "end": {"line": 482, "column": 39}}, "93": {"start": {"line": 489, "column": 8}, "end": {"line": 489, "column": 25}}, "94": {"start": {"line": 490, "column": 8}, "end": {"line": 490, "column": 14447}}, "95": {"start": {"line": 491, "column": 12}, "end": {"line": 491, "column": 14507}}, "96": {"start": {"line": 495, "column": 8}, "end": {"line": 495, "column": 20}}, "97": {"start": {"line": 516, "column": 8}, "end": {"line": 516, "column": 15262}}, "98": {"start": {"line": 517, "column": 12}, "end": {"line": 517, "column": 55}}, "99": {"start": {"line": 531, "column": 8}, "end": {"line": 531, "column": 44}}, "100": {"start": {"line": 532, "column": 8}, "end": {"line": 532, "column": 42}}, "101": {"start": {"line": 534, "column": 8}, "end": {"line": 534, "column": 58}}, "102": {"start": {"line": 535, "column": 8}, "end": {"line": 535, "column": 15930}}, "103": {"start": {"line": 539, "column": 8}, "end": {"line": 539, "column": 21}}, "104": {"start": {"line": 553, "column": 8}, "end": {"line": 553, "column": 16371}}, "105": {"start": {"line": 567, "column": 8}, "end": {"line": 567, "column": 15}}, "106": {"start": {"line": 574, "column": 8}, "end": {"line": 574, "column": 17}}, "107": {"start": {"line": 584, "column": 8}, "end": {"line": 584, "column": 80}}, "108": {"start": {"line": 586, "column": 8}, "end": {"line": 586, "column": 17055}}, "109": {"start": {"line": 587, "column": 12}, "end": {"line": 587, "column": 17120}}, "110": {"start": {"line": 591, "column": 16}, "end": {"line": 591, "column": 17328}}, "111": {"start": {"line": 595, "column": 16}, "end": {"line": 595, "column": 17452}}, "112": {"start": {"line": 612, "column": 8}, "end": {"line": 612, "column": 17916}}, "113": {"start": {"line": 614, "column": 12}, "end": {"line": 614, "column": 18021}}, "114": {"start": {"line": 637, "column": 8}, "end": {"line": 637, "column": 58}}, "115": {"start": {"line": 638, "column": 8}, "end": {"line": 638, "column": 18737}}, "116": {"start": {"line": 641, "column": 8}, "end": {"line": 641, "column": 18795}}}, "branchMap": {"1": {"line": 99, "type": "if", "locations": [{"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 8}}, {"start": {"line": 99, "column": 8}, "end": {"line": 99, "column": 8}}]}, "2": {"line": 100, "type": "if", "locations": [{"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 8}}, {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 8}}]}, "3": {"line": 120, "type": "if", "locations": [{"start": {"line": 120, "column": 15}, "end": {"line": 120, "column": 15}}, {"start": {"line": 120, "column": 15}, "end": {"line": 120, "column": 15}}]}, "4": {"line": 121, "type": "if", "locations": [{"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 8}}, {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 8}}]}, "5": {"line": 122, "type": "if", "locations": [{"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 8}}, {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 8}}]}, "6": {"line": 123, "type": "if", "locations": [{"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 8}}, {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 8}}]}, "7": {"line": 124, "type": "if", "locations": [{"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 8}}, {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 8}}]}, "8": {"line": 125, "type": "if", "locations": [{"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}, {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}]}, "9": {"line": 129, "type": "if", "locations": [{"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 12}}, {"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 12}}]}, "10": {"line": 155, "type": "if", "locations": [{"start": {"line": 155, "column": 15}, "end": {"line": 155, "column": 15}}, {"start": {"line": 155, "column": 15}, "end": {"line": 155, "column": 15}}]}, "11": {"line": 155, "type": "if", "locations": [{"start": {"line": 155, "column": 25}, "end": {"line": 155, "column": 25}}, {"start": {"line": 155, "column": 25}, "end": {"line": 155, "column": 25}}]}, "12": {"line": 156, "type": "if", "locations": [{"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 8}}, {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 8}}]}, "13": {"line": 157, "type": "if", "locations": [{"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 8}}, {"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 8}}]}, "14": {"line": 165, "type": "if", "locations": [{"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}, {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}]}, "15": {"line": 175, "type": "if", "locations": [{"start": {"line": 175, "column": 58}, "end": {"line": 175, "column": 58}}, {"start": {"line": 175, "column": 58}, "end": {"line": 175, "column": 58}}]}, "16": {"line": 175, "type": "if", "locations": [{"start": {"line": 175, "column": 68}, "end": {"line": 175, "column": 68}}, {"start": {"line": 175, "column": 68}, "end": {"line": 175, "column": 68}}]}, "17": {"line": 178, "type": "if", "locations": [{"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 8}}, {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 8}}]}, "18": {"line": 188, "type": "if", "locations": [{"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}, {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}]}, "19": {"line": 189, "type": "if", "locations": [{"start": {"line": 189, "column": 12}, "end": {"line": 189, "column": 12}}, {"start": {"line": 189, "column": 12}, "end": {"line": 189, "column": 12}}]}, "20": {"line": 196, "type": "if", "locations": [{"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 8}}, {"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 8}}]}, "21": {"line": 197, "type": "if", "locations": [{"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 8}}, {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 8}}]}, "22": {"line": 209, "type": "if", "locations": [{"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 8}}, {"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 8}}]}, "23": {"line": 218, "type": "if", "locations": [{"start": {"line": 218, "column": 46}, "end": {"line": 218, "column": 46}}, {"start": {"line": 218, "column": 46}, "end": {"line": 218, "column": 46}}]}, "24": {"line": 218, "type": "if", "locations": [{"start": {"line": 218, "column": 59}, "end": {"line": 218, "column": 59}}, {"start": {"line": 218, "column": 59}, "end": {"line": 218, "column": 59}}]}, "25": {"line": 219, "type": "if", "locations": [{"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}, {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}]}, "26": {"line": 223, "type": "if", "locations": [{"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 8}}, {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 8}}]}, "27": {"line": 256, "type": "if", "locations": [{"start": {"line": 256, "column": 12}, "end": {"line": 256, "column": 12}}, {"start": {"line": 256, "column": 12}, "end": {"line": 256, "column": 12}}]}, "28": {"line": 260, "type": "if", "locations": [{"start": {"line": 260, "column": 12}, "end": {"line": 260, "column": 12}}, {"start": {"line": 260, "column": 12}, "end": {"line": 260, "column": 12}}]}, "29": {"line": 266, "type": "if", "locations": [{"start": {"line": 266, "column": 12}, "end": {"line": 266, "column": 12}}, {"start": {"line": 266, "column": 12}, "end": {"line": 266, "column": 12}}]}, "30": {"line": 271, "type": "if", "locations": [{"start": {"line": 271, "column": 12}, "end": {"line": 271, "column": 12}}, {"start": {"line": 271, "column": 12}, "end": {"line": 271, "column": 12}}]}, "31": {"line": 277, "type": "if", "locations": [{"start": {"line": 277, "column": 12}, "end": {"line": 277, "column": 12}}, {"start": {"line": 277, "column": 12}, "end": {"line": 277, "column": 12}}]}, "32": {"line": 277, "type": "cond-expr", "locations": [{"start": {"line": 277, "column": 16}, "end": {"line": 277, "column": 25}}, {"start": {"line": 277, "column": 30}, "end": {"line": 277, "column": 41}}]}, "33": {"line": 294, "type": "if", "locations": [{"start": {"line": 294, "column": 12}, "end": {"line": 294, "column": 12}}, {"start": {"line": 294, "column": 12}, "end": {"line": 294, "column": 12}}]}, "34": {"line": 299, "type": "if", "locations": [{"start": {"line": 299, "column": 8}, "end": {"line": 299, "column": 8}}, {"start": {"line": 299, "column": 8}, "end": {"line": 299, "column": 8}}]}, "35": {"line": 323, "type": "if", "locations": [{"start": {"line": 323, "column": 8}, "end": {"line": 323, "column": 8}}, {"start": {"line": 323, "column": 8}, "end": {"line": 323, "column": 8}}]}, "36": {"line": 324, "type": "if", "locations": [{"start": {"line": 324, "column": 8}, "end": {"line": 324, "column": 8}}, {"start": {"line": 324, "column": 8}, "end": {"line": 324, "column": 8}}]}, "37": {"line": 340, "type": "if", "locations": [{"start": {"line": 340, "column": 8}, "end": {"line": 340, "column": 8}}, {"start": {"line": 340, "column": 8}, "end": {"line": 340, "column": 8}}]}, "38": {"line": 358, "type": "if", "locations": [{"start": {"line": 358, "column": 8}, "end": {"line": 358, "column": 8}}, {"start": {"line": 358, "column": 8}, "end": {"line": 358, "column": 8}}]}, "39": {"line": 377, "type": "if", "locations": [{"start": {"line": 377, "column": 8}, "end": {"line": 377, "column": 8}}, {"start": {"line": 377, "column": 8}, "end": {"line": 377, "column": 8}}]}, "40": {"line": 397, "type": "if", "locations": [{"start": {"line": 397, "column": 18}, "end": {"line": 397, "column": 72}}, {"start": {"line": 398, "column": 18}, "end": {"line": 398, "column": 72}}]}, "41": {"line": 400, "type": "if", "locations": [{"start": {"line": 400, "column": 12}, "end": {"line": 400, "column": 12}}, {"start": {"line": 400, "column": 12}, "end": {"line": 400, "column": 12}}]}, "42": {"line": 404, "type": "if", "locations": [{"start": {"line": 404, "column": 16}, "end": {"line": 404, "column": 16}}, {"start": {"line": 404, "column": 16}, "end": {"line": 404, "column": 16}}]}, "43": {"line": 417, "type": "if", "locations": [{"start": {"line": 417, "column": 8}, "end": {"line": 417, "column": 8}}, {"start": {"line": 417, "column": 8}, "end": {"line": 417, "column": 8}}]}, "44": {"line": 420, "type": "if", "locations": [{"start": {"line": 420, "column": 48}, "end": {"line": 420, "column": 85}}, {"start": {"line": 420, "column": 89}, "end": {"line": 420, "column": 89}}]}, "45": {"line": 421, "type": "if", "locations": [{"start": {"line": 421, "column": 8}, "end": {"line": 421, "column": 8}}, {"start": {"line": 421, "column": 8}, "end": {"line": 421, "column": 8}}]}, "46": {"line": 424, "type": "if", "locations": [{"start": {"line": 424, "column": 34}, "end": {"line": 424, "column": 36}}, {"start": {"line": 424, "column": 40}, "end": {"line": 424, "column": 49}}]}, "47": {"line": 437, "type": "if", "locations": [{"start": {"line": 437, "column": 8}, "end": {"line": 437, "column": 8}}, {"start": {"line": 437, "column": 8}, "end": {"line": 437, "column": 8}}]}, "48": {"line": 447, "type": "if", "locations": [{"start": {"line": 447, "column": 8}, "end": {"line": 447, "column": 8}}, {"start": {"line": 447, "column": 8}, "end": {"line": 447, "column": 8}}]}, "49": {"line": 461, "type": "if", "locations": [{"start": {"line": 461, "column": 59}, "end": {"line": 461, "column": 59}}, {"start": {"line": 461, "column": 59}, "end": {"line": 461, "column": 59}}]}, "50": {"line": 462, "type": "if", "locations": [{"start": {"line": 462, "column": 8}, "end": {"line": 462, "column": 8}}, {"start": {"line": 462, "column": 8}, "end": {"line": 462, "column": 8}}]}, "51": {"line": 474, "type": "if", "locations": [{"start": {"line": 474, "column": 48}, "end": {"line": 474, "column": 48}}, {"start": {"line": 474, "column": 48}, "end": {"line": 474, "column": 48}}]}, "52": {"line": 475, "type": "if", "locations": [{"start": {"line": 475, "column": 8}, "end": {"line": 475, "column": 8}}, {"start": {"line": 475, "column": 8}, "end": {"line": 475, "column": 8}}]}, "53": {"line": 476, "type": "if", "locations": [{"start": {"line": 476, "column": 8}, "end": {"line": 476, "column": 8}}, {"start": {"line": 476, "column": 8}, "end": {"line": 476, "column": 8}}]}, "54": {"line": 491, "type": "if", "locations": [{"start": {"line": 491, "column": 12}, "end": {"line": 491, "column": 12}}, {"start": {"line": 491, "column": 12}, "end": {"line": 491, "column": 12}}]}, "55": {"line": 532, "type": "if", "locations": [{"start": {"line": 532, "column": 8}, "end": {"line": 532, "column": 8}}, {"start": {"line": 532, "column": 8}, "end": {"line": 532, "column": 8}}]}, "56": {"line": 566, "type": "if", "locations": [{"start": {"line": 566, "column": 30}, "end": {"line": 566, "column": 30}}, {"start": {"line": 566, "column": 30}, "end": {"line": 566, "column": 30}}]}, "57": {"line": 573, "type": "if", "locations": [{"start": {"line": 573, "column": 32}, "end": {"line": 573, "column": 32}}, {"start": {"line": 573, "column": 32}, "end": {"line": 573, "column": 32}}]}, "58": {"line": 583, "type": "if", "locations": [{"start": {"line": 583, "column": 15}, "end": {"line": 583, "column": 15}}, {"start": {"line": 583, "column": 15}, "end": {"line": 583, "column": 15}}]}, "59": {"line": 584, "type": "if", "locations": [{"start": {"line": 584, "column": 8}, "end": {"line": 584, "column": 8}}, {"start": {"line": 584, "column": 8}, "end": {"line": 584, "column": 8}}]}, "60": {"line": 587, "type": "if", "locations": [{"start": {"line": 587, "column": 12}, "end": {"line": 587, "column": 12}}, {"start": {"line": 587, "column": 12}, "end": {"line": 587, "column": 12}}]}, "61": {"line": 591, "type": "if", "locations": [{"start": {"line": 591, "column": 16}, "end": {"line": 591, "column": 16}}, {"start": {"line": 591, "column": 16}, "end": {"line": 591, "column": 16}}]}, "62": {"line": 608, "type": "if", "locations": [{"start": {"line": 608, "column": 60}, "end": {"line": 608, "column": 60}}, {"start": {"line": 608, "column": 60}, "end": {"line": 608, "column": 60}}]}, "63": {"line": 608, "type": "if", "locations": [{"start": {"line": 608, "column": 70}, "end": {"line": 608, "column": 70}}, {"start": {"line": 608, "column": 70}, "end": {"line": 608, "column": 70}}]}, "64": {"line": 612, "type": "if", "locations": [{"start": {"line": 612, "column": 8}, "end": {"line": 612, "column": 8}}, {"start": {"line": 612, "column": 8}, "end": {"line": 612, "column": 8}}]}, "65": {"line": 626, "type": "if", "locations": [{"start": {"line": 626, "column": 62}, "end": {"line": 626, "column": 62}}, {"start": {"line": 626, "column": 62}, "end": {"line": 626, "column": 62}}]}}}, "contracts/HAOXPriceOracleV2.sol": {"l": {"86": 0, "87": 0, "88": 0, "89": 0, "91": 0, "92": 0, "93": 0, "94": 0, "97": 0, "104": 0, "111": 0, "112": 0, "114": 0, "115": 0, "118": 0, "119": 0, "120": 0, "123": 0, "124": 0, "132": 0, "137": 0, "140": 0, "142": 0, "143": 0, "146": 0, "149": 0, "150": 0, "152": 0, "153": 0, "154": 0, "156": 0, "159": 0, "160": 0, "168": 0, "169": 0, "172": 0, "173": 0, "175": 0, "176": 0, "180": 0, "181": 0, "188": 0, "192": 0, "199": 0, "200": 0, "203": 0, "205": 0, "206": 0, "210": 0, "212": 0, "217": 0, "224": 0, "232": 0, "233": 0, "235": 0, "243": 0, "244": 0, "245": 0, "252": 0, "253": 0, "254": 0, "261": 0, "262": 0, "269": 0, "276": 0, "283": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPriceOracleV2.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "fnMap": {"1": {"name": "constructor", "line": 85, "loc": {"start": {"line": 80, "column": 4}, "end": {"line": 98, "column": 4}}}, "2": {"name": "getBnbUsdPrice", "line": 103, "loc": {"start": {"line": 103, "column": 4}, "end": {"line": 126, "column": 4}}}, "3": {"name": "getHaoxBnbPrice", "line": 131, "loc": {"start": {"line": 131, "column": 4}, "end": {"line": 162, "column": 4}}}, "4": {"name": "getHaoxUsdPrice", "line": 167, "loc": {"start": {"line": 167, "column": 4}, "end": {"line": 182, "column": 4}}}, "5": {"name": "updatePrices", "line": 187, "loc": {"start": {"line": 187, "column": 4}, "end": {"line": 193, "column": 4}}}, "6": {"name": "_updatePrices", "line": 198, "loc": {"start": {"line": 198, "column": 4}, "end": {"line": 237, "column": 4}}}, "7": {"name": "activateEmergencyMode", "line": 242, "loc": {"start": {"line": 242, "column": 4}, "end": {"line": 246, "column": 4}}}, "8": {"name": "deactivateEmergencyMode", "line": 251, "loc": {"start": {"line": 251, "column": 4}, "end": {"line": 255, "column": 4}}}, "9": {"name": "getLatestPrice", "line": 260, "loc": {"start": {"line": 260, "column": 4}, "end": {"line": 263, "column": 4}}}, "10": {"name": "getPriceWithConfidence", "line": 268, "loc": {"start": {"line": 268, "column": 4}, "end": {"line": 270, "column": 4}}}, "11": {"name": "pause", "line": 275, "loc": {"start": {"line": 275, "column": 4}, "end": {"line": 277, "column": 4}}}, "12": {"name": "unpause", "line": 282, "loc": {"start": {"line": 282, "column": 4}, "end": {"line": 284, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 75}}, "2": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 66}}, "3": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 64}}, "4": {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 59}}, "5": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 22}}, "6": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 3028}}, "7": {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 47}}, "8": {"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 95}}, "9": {"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 55}}, "10": {"start": {"line": 118, "column": 12}, "end": {"line": 118, "column": 53}}, "11": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 3995}}, "12": {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 68}}, "13": {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 65}}, "14": {"start": {"line": 142, "column": 12}, "end": {"line": 142, "column": 86}}, "15": {"start": {"line": 143, "column": 12}, "end": {"line": 143, "column": 85}}, "16": {"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 61}}, "17": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 71}}, "18": {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 4916}}, "19": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 5325}}, "20": {"start": {"line": 169, "column": 12}, "end": {"line": 169, "column": 40}}, "21": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 71}}, "22": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 74}}, "23": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 5573}}, "24": {"start": {"line": 176, "column": 12}, "end": {"line": 176, "column": 72}}, "25": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 5987}}, "26": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 22}}, "27": {"start": {"line": 199, "column": 8}, "end": {"line": 199, "column": 71}}, "28": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 68}}, "29": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 6398}}, "30": {"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 6511}}, "31": {"start": {"line": 206, "column": 16}, "end": {"line": 206, "column": 6640}}, "32": {"start": {"line": 210, "column": 16}, "end": {"line": 210, "column": 6840}}, "33": {"start": {"line": 235, "column": 12}, "end": {"line": 235, "column": 91}}, "34": {"start": {"line": 245, "column": 8}, "end": {"line": 245, "column": 48}}, "35": {"start": {"line": 254, "column": 8}, "end": {"line": 254, "column": 39}}, "36": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 44}}, "37": {"start": {"line": 262, "column": 8}, "end": {"line": 262, "column": 20}}, "38": {"start": {"line": 269, "column": 8}, "end": {"line": 269, "column": 32}}, "39": {"start": {"line": 276, "column": 8}, "end": {"line": 276, "column": 15}}, "40": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 17}}}, "branchMap": {"1": {"line": 86, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}, {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}]}, "2": {"line": 87, "type": "if", "locations": [{"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 8}}, {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 8}}]}, "3": {"line": 88, "type": "if", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 8}}, {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 8}}]}, "4": {"line": 89, "type": "if", "locations": [{"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 8}}, {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 8}}]}, "5": {"line": 111, "type": "if", "locations": [{"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 12}}, {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 12}}]}, "6": {"line": 112, "type": "if", "locations": [{"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 12}}, {"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 12}}]}, "7": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 49}, "end": {"line": 119, "column": 51}}, {"start": {"line": 119, "column": 55}, "end": {"line": 119, "column": 83}}]}, "8": {"line": 120, "type": "if", "locations": [{"start": {"line": 120, "column": 44}, "end": {"line": 120, "column": 46}}, {"start": {"line": 120, "column": 50}, "end": {"line": 120, "column": 59}}]}, "9": {"line": 137, "type": "if", "locations": [{"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}, {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}]}, "10": {"line": 142, "type": "if", "locations": [{"start": {"line": 142, "column": 49}, "end": {"line": 142, "column": 65}}, {"start": {"line": 142, "column": 69}, "end": {"line": 142, "column": 85}}]}, "11": {"line": 143, "type": "if", "locations": [{"start": {"line": 143, "column": 48}, "end": {"line": 143, "column": 64}}, {"start": {"line": 143, "column": 68}, "end": {"line": 143, "column": 84}}]}, "12": {"line": 152, "type": "if", "locations": [{"start": {"line": 152, "column": 61}, "end": {"line": 152, "column": 62}}, {"start": {"line": 152, "column": 66}, "end": {"line": 152, "column": 67}}]}, "13": {"line": 153, "type": "if", "locations": [{"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 12}}, {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 12}}]}, "14": {"line": 156, "type": "if", "locations": [{"start": {"line": 156, "column": 44}, "end": {"line": 156, "column": 46}}, {"start": {"line": 156, "column": 50}, "end": {"line": 156, "column": 59}}]}, "15": {"line": 168, "type": "if", "locations": [{"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}, {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}]}, "16": {"line": 175, "type": "if", "locations": [{"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 8}}, {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 8}}]}, "17": {"line": 175, "type": "cond-expr", "locations": [{"start": {"line": 175, "column": 12}, "end": {"line": 175, "column": 27}}, {"start": {"line": 175, "column": 32}, "end": {"line": 175, "column": 48}}]}, "18": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 37}, "end": {"line": 187, "column": 37}}, {"start": {"line": 187, "column": 37}, "end": {"line": 187, "column": 37}}]}, "19": {"line": 188, "type": "if", "locations": [{"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}, {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}]}, "20": {"line": 203, "type": "if", "locations": [{"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 8}}, {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 8}}]}, "21": {"line": 205, "type": "if", "locations": [{"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 12}}, {"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 12}}]}, "22": {"line": 207, "type": "if", "locations": [{"start": {"line": 207, "column": 22}, "end": {"line": 207, "column": 92}}, {"start": {"line": 208, "column": 22}, "end": {"line": 208, "column": 92}}]}, "23": {"line": 210, "type": "if", "locations": [{"start": {"line": 210, "column": 16}, "end": {"line": 210, "column": 16}}, {"start": {"line": 210, "column": 16}, "end": {"line": 210, "column": 16}}]}, "24": {"line": 242, "type": "if", "locations": [{"start": {"line": 242, "column": 65}, "end": {"line": 242, "column": 65}}, {"start": {"line": 242, "column": 65}, "end": {"line": 242, "column": 65}}]}, "25": {"line": 251, "type": "if", "locations": [{"start": {"line": 251, "column": 48}, "end": {"line": 251, "column": 48}}, {"start": {"line": 251, "column": 48}, "end": {"line": 251, "column": 48}}]}, "26": {"line": 275, "type": "if", "locations": [{"start": {"line": 275, "column": 30}, "end": {"line": 275, "column": 30}}, {"start": {"line": 275, "column": 30}, "end": {"line": 275, "column": 30}}]}, "27": {"line": 282, "type": "if", "locations": [{"start": {"line": 282, "column": 32}, "end": {"line": 282, "column": 32}}, {"start": {"line": 282, "column": 32}, "end": {"line": 282, "column": 32}}]}}}, "contracts/HAOXTokenV2.sol": {"l": {"57": 0, "64": 0, "66": 0, "68": 0, "72": 0, "75": 0, "76": 0, "78": 0, "79": 0, "80": 0, "82": 0, "83": 0, "91": 0, "93": 0, "94": 0, "98": 0, "100": 0, "101": 0, "103": 0, "104": 0, "105": 0, "107": 0, "108": 0, "116": 0, "118": 0, "119": 0, "123": 0, "125": 0, "126": 0, "128": 0, "129": 0, "130": 0, "132": 0, "133": 0, "141": 0, "143": 0, "144": 0, "148": 0, "150": 0, "151": 0, "153": 0, "154": 0, "155": 0, "157": 0, "158": 0, "166": 0, "167": 0, "168": 0, "169": 0, "176": 0, "177": 0, "178": 0, "185": 0, "186": 0, "187": 0, "189": 0, "190": 0, "197": 0, "204": 0, "211": 0, "226": 0, "227": 0, "228": 0, "229": 0, "232": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXTokenV2.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "fnMap": {"1": {"name": "constructor", "line": 55, "loc": {"start": {"line": 55, "column": 4}, "end": {"line": 58, "column": 4}}}, "2": {"name": "setPresaleContract", "line": 63, "loc": {"start": {"line": 63, "column": 4}, "end": {"line": 85, "column": 4}}}, "3": {"name": "setInvitationContract", "line": 90, "loc": {"start": {"line": 90, "column": 4}, "end": {"line": 110, "column": 4}}}, "4": {"name": "setVestingContract", "line": 115, "loc": {"start": {"line": 115, "column": 4}, "end": {"line": 135, "column": 4}}}, "5": {"name": "setPriceO<PERSON>le", "line": 140, "loc": {"start": {"line": 140, "column": 4}, "end": {"line": 160, "column": 4}}}, "6": {"name": "emergencyPause", "line": 165, "loc": {"start": {"line": 165, "column": 4}, "end": {"line": 170, "column": 4}}}, "7": {"name": "unpause", "line": 175, "loc": {"start": {"line": 175, "column": 4}, "end": {"line": 179, "column": 4}}}, "8": {"name": "autoUnpause", "line": 184, "loc": {"start": {"line": 184, "column": 4}, "end": {"line": 191, "column": 4}}}, "9": {"name": "renounceOwnership", "line": 196, "loc": {"start": {"line": 196, "column": 4}, "end": {"line": 198, "column": 4}}}, "10": {"name": "transfer", "line": 203, "loc": {"start": {"line": 203, "column": 4}, "end": {"line": 205, "column": 4}}}, "11": {"name": "transferFrom", "line": 210, "loc": {"start": {"line": 210, "column": 4}, "end": {"line": 212, "column": 4}}}, "12": {"name": "getContractStatus", "line": 217, "loc": {"start": {"line": 217, "column": 4}, "end": {"line": 241, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 40}}, "2": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 109}}, "3": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 2083}}, "4": {"start": {"line": 72, "column": 12}, "end": {"line": 72, "column": 70}}, "5": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 91}}, "6": {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 67}}, "7": {"start": {"line": 78, "column": 12}, "end": {"line": 78, "column": 48}}, "8": {"start": {"line": 82, "column": 12}, "end": {"line": 82, "column": 80}}, "9": {"start": {"line": 83, "column": 12}, "end": {"line": 83, "column": 41}}, "10": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 115}}, "11": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 3151}}, "12": {"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 70}}, "13": {"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 91}}, "14": {"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 67}}, "15": {"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 51}}, "16": {"start": {"line": 107, "column": 12}, "end": {"line": 107, "column": 86}}, "17": {"start": {"line": 108, "column": 12}, "end": {"line": 108, "column": 41}}, "18": {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 109}}, "19": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 4180}}, "20": {"start": {"line": 123, "column": 12}, "end": {"line": 123, "column": 70}}, "21": {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 91}}, "22": {"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 67}}, "23": {"start": {"line": 128, "column": 12}, "end": {"line": 128, "column": 48}}, "24": {"start": {"line": 132, "column": 12}, "end": {"line": 132, "column": 80}}, "25": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 41}}, "26": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 101}}, "27": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 5179}}, "28": {"start": {"line": 148, "column": 12}, "end": {"line": 148, "column": 70}}, "29": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 91}}, "30": {"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 67}}, "31": {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 44}}, "32": {"start": {"line": 157, "column": 12}, "end": {"line": 157, "column": 80}}, "33": {"start": {"line": 158, "column": 12}, "end": {"line": 158, "column": 41}}, "34": {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 43}}, "35": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 15}}, "36": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 56}}, "37": {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 38}}, "38": {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 17}}, "39": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 38}}, "40": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 57}}, "41": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 101}}, "42": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 17}}, "43": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 32}}, "44": {"start": {"line": 204, "column": 8}, "end": {"line": 204, "column": 41}}, "45": {"start": {"line": 211, "column": 8}, "end": {"line": 211, "column": 51}}, "46": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 29}}, "47": {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 7628}}, "48": {"start": {"line": 228, "column": 12}, "end": {"line": 228, "column": 62}}, "49": {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 7847}}}, "branchMap": {"1": {"line": 63, "type": "if", "locations": [{"start": {"line": 63, "column": 67}, "end": {"line": 63, "column": 67}}, {"start": {"line": 63, "column": 67}, "end": {"line": 63, "column": 67}}]}, "2": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}, {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}]}, "3": {"line": 75, "type": "if", "locations": [{"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 12}}, {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 12}}]}, "4": {"line": 76, "type": "if", "locations": [{"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 12}}, {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 12}}]}, "5": {"line": 90, "type": "if", "locations": [{"start": {"line": 90, "column": 73}, "end": {"line": 90, "column": 73}}, {"start": {"line": 90, "column": 73}, "end": {"line": 90, "column": 73}}]}, "6": {"line": 93, "type": "if", "locations": [{"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 8}}, {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 8}}]}, "7": {"line": 100, "type": "if", "locations": [{"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 12}}, {"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 12}}]}, "8": {"line": 101, "type": "if", "locations": [{"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 12}}, {"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 12}}]}, "9": {"line": 115, "type": "if", "locations": [{"start": {"line": 115, "column": 67}, "end": {"line": 115, "column": 67}}, {"start": {"line": 115, "column": 67}, "end": {"line": 115, "column": 67}}]}, "10": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}, {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}]}, "11": {"line": 125, "type": "if", "locations": [{"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 12}}, {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 12}}]}, "12": {"line": 126, "type": "if", "locations": [{"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 12}}, {"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 12}}]}, "13": {"line": 140, "type": "if", "locations": [{"start": {"line": 140, "column": 59}, "end": {"line": 140, "column": 59}}, {"start": {"line": 140, "column": 59}, "end": {"line": 140, "column": 59}}]}, "14": {"line": 143, "type": "if", "locations": [{"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 8}}, {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 8}}]}, "15": {"line": 150, "type": "if", "locations": [{"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 12}}, {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 12}}]}, "16": {"line": 151, "type": "if", "locations": [{"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 12}}, {"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 12}}]}, "17": {"line": 165, "type": "if", "locations": [{"start": {"line": 165, "column": 39}, "end": {"line": 165, "column": 39}}, {"start": {"line": 165, "column": 39}, "end": {"line": 165, "column": 39}}]}, "18": {"line": 166, "type": "if", "locations": [{"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}, {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}]}, "19": {"line": 175, "type": "if", "locations": [{"start": {"line": 175, "column": 32}, "end": {"line": 175, "column": 32}}, {"start": {"line": 175, "column": 32}, "end": {"line": 175, "column": 32}}]}, "20": {"line": 176, "type": "if", "locations": [{"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 8}}, {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 8}}]}, "21": {"line": 185, "type": "if", "locations": [{"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 8}}, {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 8}}]}, "22": {"line": 186, "type": "if", "locations": [{"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 8}}, {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 8}}]}, "23": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}, {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}]}, "24": {"line": 196, "type": "if", "locations": [{"start": {"line": 196, "column": 49}, "end": {"line": 196, "column": 49}}, {"start": {"line": 196, "column": 49}, "end": {"line": 196, "column": 49}}]}, "25": {"line": 203, "type": "if", "locations": [{"start": {"line": 203, "column": 66}, "end": {"line": 203, "column": 66}}, {"start": {"line": 203, "column": 66}, "end": {"line": 203, "column": 66}}]}, "26": {"line": 210, "type": "if", "locations": [{"start": {"line": 210, "column": 84}, "end": {"line": 210, "column": 84}}, {"start": {"line": 210, "column": 84}, "end": {"line": 210, "column": 84}}]}, "27": {"line": 227, "type": "if", "locations": [{"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 8}}, {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 8}}]}, "28": {"line": 229, "type": "if", "locations": [{"start": {"line": 229, "column": 55}, "end": {"line": 229, "column": 82}}, {"start": {"line": 229, "column": 86}, "end": {"line": 229, "column": 86}}]}, "29": {"line": 234, "type": "if", "locations": [{"start": {"line": 234, "column": 33}, "end": {"line": 234, "column": 64}}, {"start": {"line": 234, "column": 68}, "end": {"line": 234, "column": 68}}]}}}, "contracts/HAOXTokenV2Test.sol": {"l": {"50": 0, "57": 0, "59": 0, "61": 0, "65": 0, "68": 0, "69": 0, "71": 0, "72": 0, "73": 0, "75": 0, "76": 0, "84": 0, "86": 0, "87": 0, "91": 0, "93": 0, "94": 0, "96": 0, "97": 0, "98": 0, "100": 0, "101": 0, "109": 0, "111": 0, "112": 0, "116": 0, "118": 0, "119": 0, "121": 0, "122": 0, "123": 0, "125": 0, "126": 0, "134": 0, "136": 0, "137": 0, "141": 0, "143": 0, "144": 0, "146": 0, "147": 0, "148": 0, "150": 0, "151": 0, "159": 0, "160": 0, "161": 0, "162": 0, "169": 0, "170": 0, "171": 0, "178": 0, "179": 0, "180": 0, "182": 0, "183": 0, "190": 0, "197": 0, "204": 0, "219": 0, "220": 0, "221": 0, "222": 0, "225": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXTokenV2Test.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "fnMap": {"1": {"name": "constructor", "line": 48, "loc": {"start": {"line": 48, "column": 4}, "end": {"line": 51, "column": 4}}}, "2": {"name": "setPresaleContract", "line": 56, "loc": {"start": {"line": 56, "column": 4}, "end": {"line": 78, "column": 4}}}, "3": {"name": "setInvitationContract", "line": 83, "loc": {"start": {"line": 83, "column": 4}, "end": {"line": 103, "column": 4}}}, "4": {"name": "setVestingContract", "line": 108, "loc": {"start": {"line": 108, "column": 4}, "end": {"line": 128, "column": 4}}}, "5": {"name": "setPriceO<PERSON>le", "line": 133, "loc": {"start": {"line": 133, "column": 4}, "end": {"line": 153, "column": 4}}}, "6": {"name": "emergencyPause", "line": 158, "loc": {"start": {"line": 158, "column": 4}, "end": {"line": 163, "column": 4}}}, "7": {"name": "unpause", "line": 168, "loc": {"start": {"line": 168, "column": 4}, "end": {"line": 172, "column": 4}}}, "8": {"name": "autoUnpause", "line": 177, "loc": {"start": {"line": 177, "column": 4}, "end": {"line": 184, "column": 4}}}, "9": {"name": "renounceOwnership", "line": 189, "loc": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 4}}}, "10": {"name": "transfer", "line": 196, "loc": {"start": {"line": 196, "column": 4}, "end": {"line": 198, "column": 4}}}, "11": {"name": "transferFrom", "line": 203, "loc": {"start": {"line": 203, "column": 4}, "end": {"line": 205, "column": 4}}}, "12": {"name": "getContractStatus", "line": 210, "loc": {"start": {"line": 210, "column": 4}, "end": {"line": 234, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 40}}, "2": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 109}}, "3": {"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": 1832}}, "4": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 70}}, "5": {"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 91}}, "6": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 67}}, "7": {"start": {"line": 71, "column": 12}, "end": {"line": 71, "column": 48}}, "8": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 80}}, "9": {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 41}}, "10": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 115}}, "11": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 2900}}, "12": {"start": {"line": 91, "column": 12}, "end": {"line": 91, "column": 70}}, "13": {"start": {"line": 93, "column": 12}, "end": {"line": 93, "column": 91}}, "14": {"start": {"line": 94, "column": 12}, "end": {"line": 94, "column": 67}}, "15": {"start": {"line": 96, "column": 12}, "end": {"line": 96, "column": 51}}, "16": {"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 86}}, "17": {"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 41}}, "18": {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 109}}, "19": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 3929}}, "20": {"start": {"line": 116, "column": 12}, "end": {"line": 116, "column": 70}}, "21": {"start": {"line": 118, "column": 12}, "end": {"line": 118, "column": 91}}, "22": {"start": {"line": 119, "column": 12}, "end": {"line": 119, "column": 67}}, "23": {"start": {"line": 121, "column": 12}, "end": {"line": 121, "column": 48}}, "24": {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 80}}, "25": {"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 41}}, "26": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 101}}, "27": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 4928}}, "28": {"start": {"line": 141, "column": 12}, "end": {"line": 141, "column": 70}}, "29": {"start": {"line": 143, "column": 12}, "end": {"line": 143, "column": 91}}, "30": {"start": {"line": 144, "column": 12}, "end": {"line": 144, "column": 67}}, "31": {"start": {"line": 146, "column": 12}, "end": {"line": 146, "column": 44}}, "32": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 80}}, "33": {"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 41}}, "34": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 43}}, "35": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 15}}, "36": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 56}}, "37": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 38}}, "38": {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 17}}, "39": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 38}}, "40": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 57}}, "41": {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 101}}, "42": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 17}}, "43": {"start": {"line": 190, "column": 8}, "end": {"line": 190, "column": 32}}, "44": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 41}}, "45": {"start": {"line": 204, "column": 8}, "end": {"line": 204, "column": 51}}, "46": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 29}}, "47": {"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 7383}}, "48": {"start": {"line": 221, "column": 12}, "end": {"line": 221, "column": 62}}, "49": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 7602}}}, "branchMap": {"1": {"line": 56, "type": "if", "locations": [{"start": {"line": 56, "column": 67}, "end": {"line": 56, "column": 67}}, {"start": {"line": 56, "column": 67}, "end": {"line": 56, "column": 67}}]}, "2": {"line": 59, "type": "if", "locations": [{"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": 8}}, {"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": 8}}]}, "3": {"line": 68, "type": "if", "locations": [{"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 12}}, {"start": {"line": 68, "column": 12}, "end": {"line": 68, "column": 12}}]}, "4": {"line": 69, "type": "if", "locations": [{"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 12}}, {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 12}}]}, "5": {"line": 83, "type": "if", "locations": [{"start": {"line": 83, "column": 73}, "end": {"line": 83, "column": 73}}, {"start": {"line": 83, "column": 73}, "end": {"line": 83, "column": 73}}]}, "6": {"line": 86, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}, {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}]}, "7": {"line": 93, "type": "if", "locations": [{"start": {"line": 93, "column": 12}, "end": {"line": 93, "column": 12}}, {"start": {"line": 93, "column": 12}, "end": {"line": 93, "column": 12}}]}, "8": {"line": 94, "type": "if", "locations": [{"start": {"line": 94, "column": 12}, "end": {"line": 94, "column": 12}}, {"start": {"line": 94, "column": 12}, "end": {"line": 94, "column": 12}}]}, "9": {"line": 108, "type": "if", "locations": [{"start": {"line": 108, "column": 67}, "end": {"line": 108, "column": 67}}, {"start": {"line": 108, "column": 67}, "end": {"line": 108, "column": 67}}]}, "10": {"line": 111, "type": "if", "locations": [{"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 8}}, {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 8}}]}, "11": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 12}, "end": {"line": 118, "column": 12}}, {"start": {"line": 118, "column": 12}, "end": {"line": 118, "column": 12}}]}, "12": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 12}, "end": {"line": 119, "column": 12}}, {"start": {"line": 119, "column": 12}, "end": {"line": 119, "column": 12}}]}, "13": {"line": 133, "type": "if", "locations": [{"start": {"line": 133, "column": 59}, "end": {"line": 133, "column": 59}}, {"start": {"line": 133, "column": 59}, "end": {"line": 133, "column": 59}}]}, "14": {"line": 136, "type": "if", "locations": [{"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 8}}, {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 8}}]}, "15": {"line": 143, "type": "if", "locations": [{"start": {"line": 143, "column": 12}, "end": {"line": 143, "column": 12}}, {"start": {"line": 143, "column": 12}, "end": {"line": 143, "column": 12}}]}, "16": {"line": 144, "type": "if", "locations": [{"start": {"line": 144, "column": 12}, "end": {"line": 144, "column": 12}}, {"start": {"line": 144, "column": 12}, "end": {"line": 144, "column": 12}}]}, "17": {"line": 158, "type": "if", "locations": [{"start": {"line": 158, "column": 39}, "end": {"line": 158, "column": 39}}, {"start": {"line": 158, "column": 39}, "end": {"line": 158, "column": 39}}]}, "18": {"line": 159, "type": "if", "locations": [{"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 8}}, {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 8}}]}, "19": {"line": 168, "type": "if", "locations": [{"start": {"line": 168, "column": 32}, "end": {"line": 168, "column": 32}}, {"start": {"line": 168, "column": 32}, "end": {"line": 168, "column": 32}}]}, "20": {"line": 169, "type": "if", "locations": [{"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 8}}, {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 8}}]}, "21": {"line": 178, "type": "if", "locations": [{"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 8}}, {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 8}}]}, "22": {"line": 179, "type": "if", "locations": [{"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}, {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}]}, "23": {"line": 180, "type": "if", "locations": [{"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 8}}, {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 8}}]}, "24": {"line": 189, "type": "if", "locations": [{"start": {"line": 189, "column": 49}, "end": {"line": 189, "column": 49}}, {"start": {"line": 189, "column": 49}, "end": {"line": 189, "column": 49}}]}, "25": {"line": 196, "type": "if", "locations": [{"start": {"line": 196, "column": 66}, "end": {"line": 196, "column": 66}}, {"start": {"line": 196, "column": 66}, "end": {"line": 196, "column": 66}}]}, "26": {"line": 203, "type": "if", "locations": [{"start": {"line": 203, "column": 84}, "end": {"line": 203, "column": 84}}, {"start": {"line": 203, "column": 84}, "end": {"line": 203, "column": 84}}]}, "27": {"line": 220, "type": "if", "locations": [{"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 8}}, {"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 8}}]}, "28": {"line": 222, "type": "if", "locations": [{"start": {"line": 222, "column": 55}, "end": {"line": 222, "column": 82}}, {"start": {"line": 222, "column": 86}, "end": {"line": 222, "column": 86}}]}, "29": {"line": 227, "type": "if", "locations": [{"start": {"line": 227, "column": 33}, "end": {"line": 227, "column": 64}}, {"start": {"line": 227, "column": 68}, "end": {"line": 227, "column": 68}}]}}}, "contracts/HAOXVestingV2.sol": {"l": {"77": 0, "78": 0, "79": 0, "80": 0, "82": 0, "83": 0, "84": 0, "85": 0, "88": 0, "95": 0, "98": 0, "99": 0, "103": 0, "104": 0, "105": 0, "109": 0, "110": 0, "111": 0, "119": 0, "120": 0, "122": 0, "133": 0, "135": 0, "142": 0, "143": 0, "146": 0, "147": 0, "149": 0, "151": 0, "152": 0, "154": 0, "158": 0, "159": 0, "161": 0, "162": 0, "165": 0, "166": 0, "177": 0, "178": 0, "179": 0, "182": 0, "183": 0, "186": 0, "189": 0, "193": 0, "198": 0, "206": 0, "207": 0, "223": 0, "225": 0, "226": 0, "247": 0, "248": 0, "249": 0, "251": 0, "252": 0, "253": 0, "261": 0, "262": 0, "263": 0, "264": 0, "271": 0, "272": 0, "273": 0, "274": 0, "281": 0, "282": 0, "289": 0, "296": 0, "303": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "fnMap": {"1": {"name": "constructor", "line": 76, "loc": {"start": {"line": 71, "column": 4}, "end": {"line": 89, "column": 4}}}, "2": {"name": "_initializeRounds", "line": 94, "loc": {"start": {"line": 94, "column": 4}, "end": {"line": 113, "column": 4}}}, "3": {"name": "_createRound", "line": 118, "loc": {"start": {"line": 118, "column": 4}, "end": {"line": 136, "column": 4}}}, "4": {"name": "checkPriceConditions", "line": 141, "loc": {"start": {"line": 141, "column": 4}, "end": {"line": 171, "column": 4}}}, "5": {"name": "_unlockRound", "line": 176, "loc": {"start": {"line": 176, "column": 4}, "end": {"line": 209, "column": 4}}}, "6": {"name": "getRoundInfo", "line": 214, "loc": {"start": {"line": 214, "column": 4}, "end": {"line": 235, "column": 4}}}, "7": {"name": "getCurrentStatus", "line": 240, "loc": {"start": {"line": 240, "column": 4}, "end": {"line": 255, "column": 4}}}, "8": {"name": "setProjectWallet", "line": 260, "loc": {"start": {"line": 260, "column": 4}, "end": {"line": 265, "column": 4}}}, "9": {"name": "setCommunityWallet", "line": 270, "loc": {"start": {"line": 270, "column": 4}, "end": {"line": 275, "column": 4}}}, "10": {"name": "setPriceO<PERSON>le", "line": 280, "loc": {"start": {"line": 280, "column": 4}, "end": {"line": 283, "column": 4}}}, "11": {"name": "pause", "line": 288, "loc": {"start": {"line": 288, "column": 4}, "end": {"line": 290, "column": 4}}}, "12": {"name": "unpause", "line": 295, "loc": {"start": {"line": 295, "column": 4}, "end": {"line": 297, "column": 4}}}, "13": {"name": "emergencyWithdraw", "line": 302, "loc": {"start": {"line": 302, "column": 4}, "end": {"line": 304, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 65}}, "2": {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 68}}, "3": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 70}}, "4": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 74}}, "5": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 26}}, "6": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 52}}, "7": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 3243}}, "8": {"start": {"line": 99, "column": 12}, "end": {"line": 99, "column": 40}}, "9": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 3378}}, "10": {"start": {"line": 105, "column": 12}, "end": {"line": 105, "column": 40}}, "11": {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 3586}}, "12": {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 40}}, "13": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 72}}, "14": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 76}}, "15": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 56}}, "16": {"start": {"line": 142, "column": 8}, "end": {"line": 142, "column": 89}}, "17": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 60}}, "18": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 4825}}, "19": {"start": {"line": 147, "column": 12}, "end": {"line": 147, "column": 66}}, "20": {"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 4958}}, "21": {"start": {"line": 154, "column": 16}, "end": {"line": 154, "column": 83}}, "22": {"start": {"line": 158, "column": 12}, "end": {"line": 158, "column": 5329}}, "23": {"start": {"line": 159, "column": 16}, "end": {"line": 159, "column": 5399}}, "24": {"start": {"line": 161, "column": 20}, "end": {"line": 161, "column": 5533}}, "25": {"start": {"line": 162, "column": 24}, "end": {"line": 162, "column": 49}}, "26": {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 61}}, "27": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 57}}, "28": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 66}}, "29": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 6359}}, "30": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 6493}}, "31": {"start": {"line": 198, "column": 8}, "end": {"line": 198, "column": 6643}}, "32": {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 6829}}, "33": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 80}}, "34": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 60}}, "35": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 7400}}, "36": {"start": {"line": 251, "column": 8}, "end": {"line": 251, "column": 8117}}, "37": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 63}}, "38": {"start": {"line": 262, "column": 8}, "end": {"line": 262, "column": 41}}, "39": {"start": {"line": 264, "column": 8}, "end": {"line": 264, "column": 64}}, "40": {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 65}}, "41": {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 43}}, "42": {"start": {"line": 274, "column": 8}, "end": {"line": 274, "column": 68}}, "43": {"start": {"line": 281, "column": 8}, "end": {"line": 281, "column": 61}}, "44": {"start": {"line": 289, "column": 8}, "end": {"line": 289, "column": 15}}, "45": {"start": {"line": 296, "column": 8}, "end": {"line": 296, "column": 17}}, "46": {"start": {"line": 303, "column": 8}, "end": {"line": 303, "column": 70}}}, "branchMap": {"1": {"line": 77, "type": "if", "locations": [{"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 8}}, {"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 8}}]}, "2": {"line": 78, "type": "if", "locations": [{"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 8}}, {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 8}}]}, "3": {"line": 79, "type": "if", "locations": [{"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 8}}, {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 8}}]}, "4": {"line": 80, "type": "if", "locations": [{"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 8}}, {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 8}}]}, "5": {"line": 141, "type": "if", "locations": [{"start": {"line": 141, "column": 45}, "end": {"line": 141, "column": 45}}, {"start": {"line": 141, "column": 45}, "end": {"line": 141, "column": 45}}]}, "6": {"line": 143, "type": "if", "locations": [{"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 8}}, {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 8}}]}, "7": {"line": 146, "type": "if", "locations": [{"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 8}}, {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 8}}]}, "8": {"line": 149, "type": "if", "locations": [{"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 12}}, {"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 12}}]}, "9": {"line": 158, "type": "if", "locations": [{"start": {"line": 158, "column": 12}, "end": {"line": 158, "column": 12}}, {"start": {"line": 158, "column": 12}, "end": {"line": 158, "column": 12}}]}, "10": {"line": 159, "type": "if", "locations": [{"start": {"line": 159, "column": 16}, "end": {"line": 159, "column": 16}}, {"start": {"line": 159, "column": 16}, "end": {"line": 159, "column": 16}}]}, "11": {"line": 161, "type": "if", "locations": [{"start": {"line": 161, "column": 20}, "end": {"line": 161, "column": 20}}, {"start": {"line": 161, "column": 20}, "end": {"line": 161, "column": 20}}]}, "12": {"line": 178, "type": "if", "locations": [{"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 8}}, {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 8}}]}, "13": {"line": 179, "type": "if", "locations": [{"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}, {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}]}, "14": {"line": 189, "type": "if", "locations": [{"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}, {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}]}, "15": {"line": 193, "type": "if", "locations": [{"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 8}}, {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 8}}]}, "16": {"line": 206, "type": "if", "locations": [{"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 8}}, {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 8}}]}, "17": {"line": 223, "type": "if", "locations": [{"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 8}}, {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 8}}]}, "18": {"line": 251, "type": "if", "locations": [{"start": {"line": 251, "column": 8}, "end": {"line": 251, "column": 8}}, {"start": {"line": 251, "column": 8}, "end": {"line": 251, "column": 8}}]}, "19": {"line": 260, "type": "if", "locations": [{"start": {"line": 260, "column": 63}, "end": {"line": 260, "column": 63}}, {"start": {"line": 260, "column": 63}, "end": {"line": 260, "column": 63}}]}, "20": {"line": 261, "type": "if", "locations": [{"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 8}}, {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 8}}]}, "21": {"line": 270, "type": "if", "locations": [{"start": {"line": 270, "column": 67}, "end": {"line": 270, "column": 67}}, {"start": {"line": 270, "column": 67}, "end": {"line": 270, "column": 67}}]}, "22": {"line": 271, "type": "if", "locations": [{"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 8}}, {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 8}}]}, "23": {"line": 280, "type": "if", "locations": [{"start": {"line": 280, "column": 59}, "end": {"line": 280, "column": 59}}, {"start": {"line": 280, "column": 59}, "end": {"line": 280, "column": 59}}]}, "24": {"line": 281, "type": "if", "locations": [{"start": {"line": 281, "column": 8}, "end": {"line": 281, "column": 8}}, {"start": {"line": 281, "column": 8}, "end": {"line": 281, "column": 8}}]}, "25": {"line": 288, "type": "if", "locations": [{"start": {"line": 288, "column": 30}, "end": {"line": 288, "column": 30}}, {"start": {"line": 288, "column": 30}, "end": {"line": 288, "column": 30}}]}, "26": {"line": 295, "type": "if", "locations": [{"start": {"line": 295, "column": 32}, "end": {"line": 295, "column": 32}}, {"start": {"line": 295, "column": 32}, "end": {"line": 295, "column": 32}}]}, "27": {"line": 302, "type": "if", "locations": [{"start": {"line": 302, "column": 56}, "end": {"line": 302, "column": 56}}, {"start": {"line": 302, "column": 56}, "end": {"line": 302, "column": 56}}]}, "28": {"line": 303, "type": "if", "locations": [{"start": {"line": 303, "column": 8}, "end": {"line": 303, "column": 8}}, {"start": {"line": 303, "column": 8}, "end": {"line": 303, "column": 8}}]}}}, "contracts/HAOXVestingV2Fixed.sol": {"l": {"97": 0, "98": 0, "107": 0, "108": 0, "109": 0, "110": 0, "112": 0, "113": 0, "114": 0, "115": 0, "118": 0, "125": 0, "128": 0, "140": 0, "141": 0, "142": 0, "146": 0, "147": 0, "148": 0, "152": 0, "153": 0, "154": 0, "162": 0, "163": 0, "165": 0, "181": 0, "182": 0, "184": 0, "185": 0, "187": 0, "188": 0, "191": 0, "198": 0, "200": 0, "201": 0, "203": 0, "204": 0, "205": 0, "208": 0, "209": 0, "214": 0, "215": 0, "216": 0, "217": 0, "226": 0, "227": 0, "228": 0, "229": 0, "234": 0, "235": 0, "236": 0, "239": 0, "243": 0, "248": 0, "269": 0, "270": 0, "292": 0, "293": 0, "295": 0, "296": 0, "297": 0, "298": 0, "299": 0, "301": 0, "302": 0, "303": 0, "314": 0, "315": 0, "317": 0, "318": 0, "321": 0, "322": 0, "323": 0, "326": 0, "339": 0, "340": 0, "341": 0, "342": 0, "343": 0, "345": 0, "347": 0, "348": 0, "352": 0, "353": 0, "360": 0, "361": 0, "362": 0, "363": 0, "370": 0, "371": 0, "372": 0, "373": 0, "380": 0, "381": 0, "382": 0, "383": 0, "390": 0, "397": 0, "404": 0, "405": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2Fixed.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "fnMap": {"1": {"name": "validRound", "line": 96, "loc": {"start": {"line": 96, "column": 4}, "end": {"line": 99, "column": 4}}}, "2": {"name": "constructor", "line": 106, "loc": {"start": {"line": 101, "column": 4}, "end": {"line": 119, "column": 4}}}, "3": {"name": "_initializeRounds", "line": 124, "loc": {"start": {"line": 124, "column": 4}, "end": {"line": 156, "column": 4}}}, "4": {"name": "_createRound", "line": 161, "loc": {"start": {"line": 161, "column": 4}, "end": {"line": 175, "column": 4}}}, "5": {"name": "checkPriceCondition", "line": 180, "loc": {"start": {"line": 180, "column": 4}, "end": {"line": 220, "column": 4}}}, "6": {"name": "_unlockRound", "line": 225, "loc": {"start": {"line": 225, "column": 4}, "end": {"line": 255, "column": 4}}}, "7": {"name": "getRoundInfo", "line": 260, "loc": {"start": {"line": 260, "column": 4}, "end": {"line": 279, "column": 4}}}, "8": {"name": "getUnlockProgress", "line": 284, "loc": {"start": {"line": 284, "column": 4}, "end": {"line": 307, "column": 4}}}, "9": {"name": "getPriceCheckHistory", "line": 313, "loc": {"start": {"line": 312, "column": 4}, "end": {"line": 327, "column": 4}}}, "10": {"name": "getUnlockStatistics", "line": 332, "loc": {"start": {"line": 332, "column": 4}, "end": {"line": 354, "column": 4}}}, "11": {"name": "updatePriceOracle", "line": 359, "loc": {"start": {"line": 359, "column": 4}, "end": {"line": 364, "column": 4}}}, "12": {"name": "updateProjectWallet", "line": 369, "loc": {"start": {"line": 369, "column": 4}, "end": {"line": 374, "column": 4}}}, "13": {"name": "updateCommunityWallet", "line": 379, "loc": {"start": {"line": 379, "column": 4}, "end": {"line": 384, "column": 4}}}, "14": {"name": "pause", "line": 389, "loc": {"start": {"line": 389, "column": 4}, "end": {"line": 391, "column": 4}}}, "15": {"name": "unpause", "line": 396, "loc": {"start": {"line": 396, "column": 4}, "end": {"line": 398, "column": 4}}}, "16": {"name": "emergencyWithdraw", "line": 403, "loc": {"start": {"line": 403, "column": 4}, "end": {"line": 406, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 87}}, "2": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 65}}, "3": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 68}}, "4": {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 70}}, "5": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 74}}, "6": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 26}}, "7": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 41}}, "8": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 4545}}, "9": {"start": {"line": 142, "column": 12}, "end": {"line": 142, "column": 40}}, "10": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 4751}}, "11": {"start": {"line": 148, "column": 12}, "end": {"line": 148, "column": 40}}, "12": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 4959}}, "13": {"start": {"line": 154, "column": 12}, "end": {"line": 154, "column": 40}}, "14": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 72}}, "15": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 76}}, "16": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 44}}, "17": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 44}}, "18": {"start": {"line": 182, "column": 38}, "end": {"line": 182, "column": 44}}, "19": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 59}}, "20": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 34}}, "21": {"start": {"line": 185, "column": 28}, "end": {"line": 185, "column": 34}}, "22": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 59}}, "23": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 62}}, "24": {"start": {"line": 191, "column": 8}, "end": {"line": 191, "column": 6250}}, "25": {"start": {"line": 198, "column": 8}, "end": {"line": 198, "column": 101}}, "26": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 6594}}, "27": {"start": {"line": 201, "column": 12}, "end": {"line": 201, "column": 6630}}, "28": {"start": {"line": 205, "column": 16}, "end": {"line": 205, "column": 80}}, "29": {"start": {"line": 208, "column": 16}, "end": {"line": 208, "column": 6951}}, "30": {"start": {"line": 209, "column": 20}, "end": {"line": 209, "column": 42}}, "31": {"start": {"line": 214, "column": 12}, "end": {"line": 214, "column": 7154}}, "32": {"start": {"line": 217, "column": 16}, "end": {"line": 217, "column": 82}}, "33": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 61}}, "34": {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 57}}, "35": {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 66}}, "36": {"start": {"line": 229, "column": 8}, "end": {"line": 229, "column": 7686}}, "37": {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 7994}}, "38": {"start": {"line": 243, "column": 8}, "end": {"line": 243, "column": 8128}}, "39": {"start": {"line": 248, "column": 8}, "end": {"line": 248, "column": 8278}}, "40": {"start": {"line": 269, "column": 8}, "end": {"line": 269, "column": 60}}, "41": {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 8891}}, "42": {"start": {"line": 295, "column": 8}, "end": {"line": 295, "column": 9580}}, "43": {"start": {"line": 296, "column": 12}, "end": {"line": 296, "column": 72}}, "44": {"start": {"line": 301, "column": 12}, "end": {"line": 301, "column": 9911}}, "45": {"start": {"line": 302, "column": 16}, "end": {"line": 302, "column": 78}}, "46": {"start": {"line": 314, "column": 8}, "end": {"line": 314, "column": 68}}, "47": {"start": {"line": 315, "column": 8}, "end": {"line": 315, "column": 39}}, "48": {"start": {"line": 317, "column": 8}, "end": {"line": 317, "column": 10554}}, "49": {"start": {"line": 321, "column": 8}, "end": {"line": 321, "column": 60}}, "50": {"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 10707}}, "51": {"start": {"line": 326, "column": 8}, "end": {"line": 326, "column": 21}}, "52": {"start": {"line": 339, "column": 8}, "end": {"line": 339, "column": 11143}}, "53": {"start": {"line": 340, "column": 12}, "end": {"line": 340, "column": 11205}}, "54": {"start": {"line": 342, "column": 16}, "end": {"line": 342, "column": 11296}}, "55": {"start": {"line": 352, "column": 8}, "end": {"line": 352, "column": 90}}, "56": {"start": {"line": 360, "column": 8}, "end": {"line": 360, "column": 66}}, "57": {"start": {"line": 361, "column": 8}, "end": {"line": 361, "column": 48}}, "58": {"start": {"line": 363, "column": 8}, "end": {"line": 363, "column": 64}}, "59": {"start": {"line": 370, "column": 8}, "end": {"line": 370, "column": 66}}, "60": {"start": {"line": 371, "column": 8}, "end": {"line": 371, "column": 41}}, "61": {"start": {"line": 373, "column": 8}, "end": {"line": 373, "column": 60}}, "62": {"start": {"line": 380, "column": 8}, "end": {"line": 380, "column": 66}}, "63": {"start": {"line": 381, "column": 8}, "end": {"line": 381, "column": 43}}, "64": {"start": {"line": 383, "column": 8}, "end": {"line": 383, "column": 62}}, "65": {"start": {"line": 390, "column": 8}, "end": {"line": 390, "column": 15}}, "66": {"start": {"line": 397, "column": 8}, "end": {"line": 397, "column": 17}}, "67": {"start": {"line": 404, "column": 8}, "end": {"line": 404, "column": 60}}, "68": {"start": {"line": 405, "column": 8}, "end": {"line": 405, "column": 74}}}, "branchMap": {"1": {"line": 97, "type": "if", "locations": [{"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}, {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}]}, "2": {"line": 107, "type": "if", "locations": [{"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 8}}, {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 8}}]}, "3": {"line": 108, "type": "if", "locations": [{"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}, {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}]}, "4": {"line": 109, "type": "if", "locations": [{"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 8}}, {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 8}}]}, "5": {"line": 110, "type": "if", "locations": [{"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 8}}, {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 8}}]}, "6": {"line": 180, "type": "if", "locations": [{"start": {"line": 180, "column": 50}, "end": {"line": 180, "column": 50}}, {"start": {"line": 180, "column": 50}, "end": {"line": 180, "column": 50}}]}, "7": {"line": 182, "type": "if", "locations": [{"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}, {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}]}, "8": {"line": 185, "type": "if", "locations": [{"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 8}}, {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 8}}]}, "9": {"line": 200, "type": "if", "locations": [{"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}, {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}]}, "10": {"line": 201, "type": "if", "locations": [{"start": {"line": 201, "column": 12}, "end": {"line": 201, "column": 12}}, {"start": {"line": 201, "column": 12}, "end": {"line": 201, "column": 12}}]}, "11": {"line": 208, "type": "if", "locations": [{"start": {"line": 208, "column": 16}, "end": {"line": 208, "column": 16}}, {"start": {"line": 208, "column": 16}, "end": {"line": 208, "column": 16}}]}, "12": {"line": 214, "type": "if", "locations": [{"start": {"line": 214, "column": 12}, "end": {"line": 214, "column": 12}}, {"start": {"line": 214, "column": 12}, "end": {"line": 214, "column": 12}}]}, "13": {"line": 227, "type": "if", "locations": [{"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 8}}, {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 8}}]}, "14": {"line": 228, "type": "if", "locations": [{"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 8}}, {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 8}}]}, "15": {"line": 229, "type": "if", "locations": [{"start": {"line": 229, "column": 8}, "end": {"line": 229, "column": 8}}, {"start": {"line": 229, "column": 8}, "end": {"line": 229, "column": 8}}]}, "16": {"line": 239, "type": "if", "locations": [{"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 8}}, {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 8}}]}, "17": {"line": 243, "type": "if", "locations": [{"start": {"line": 243, "column": 8}, "end": {"line": 243, "column": 8}}, {"start": {"line": 243, "column": 8}, "end": {"line": 243, "column": 8}}]}, "18": {"line": 260, "type": "if", "locations": [{"start": {"line": 260, "column": 61}, "end": {"line": 260, "column": 61}}, {"start": {"line": 260, "column": 61}, "end": {"line": 260, "column": 61}}]}, "19": {"line": 295, "type": "if", "locations": [{"start": {"line": 295, "column": 8}, "end": {"line": 295, "column": 8}}, {"start": {"line": 295, "column": 8}, "end": {"line": 295, "column": 8}}]}, "20": {"line": 301, "type": "if", "locations": [{"start": {"line": 301, "column": 12}, "end": {"line": 301, "column": 12}}, {"start": {"line": 301, "column": 12}, "end": {"line": 301, "column": 12}}]}, "21": {"line": 303, "type": "if", "locations": [{"start": {"line": 303, "column": 70}, "end": {"line": 303, "column": 70}}, {"start": {"line": 304, "column": 31}, "end": {"line": 304, "column": 64}}]}, "22": {"line": 313, "type": "if", "locations": [{"start": {"line": 313, "column": 30}, "end": {"line": 313, "column": 30}}, {"start": {"line": 313, "column": 30}, "end": {"line": 313, "column": 30}}]}, "23": {"line": 317, "type": "if", "locations": [{"start": {"line": 317, "column": 8}, "end": {"line": 317, "column": 8}}, {"start": {"line": 317, "column": 8}, "end": {"line": 317, "column": 8}}]}, "24": {"line": 317, "type": "cond-expr", "locations": [{"start": {"line": 317, "column": 12}, "end": {"line": 317, "column": 21}}, {"start": {"line": 317, "column": 26}, "end": {"line": 317, "column": 39}}]}, "25": {"line": 340, "type": "if", "locations": [{"start": {"line": 340, "column": 12}, "end": {"line": 340, "column": 12}}, {"start": {"line": 340, "column": 12}, "end": {"line": 340, "column": 12}}]}, "26": {"line": 342, "type": "if", "locations": [{"start": {"line": 342, "column": 16}, "end": {"line": 342, "column": 16}}, {"start": {"line": 342, "column": 16}, "end": {"line": 342, "column": 16}}]}, "27": {"line": 359, "type": "if", "locations": [{"start": {"line": 359, "column": 60}, "end": {"line": 359, "column": 60}}, {"start": {"line": 359, "column": 60}, "end": {"line": 359, "column": 60}}]}, "28": {"line": 360, "type": "if", "locations": [{"start": {"line": 360, "column": 8}, "end": {"line": 360, "column": 8}}, {"start": {"line": 360, "column": 8}, "end": {"line": 360, "column": 8}}]}, "29": {"line": 369, "type": "if", "locations": [{"start": {"line": 369, "column": 62}, "end": {"line": 369, "column": 62}}, {"start": {"line": 369, "column": 62}, "end": {"line": 369, "column": 62}}]}, "30": {"line": 370, "type": "if", "locations": [{"start": {"line": 370, "column": 8}, "end": {"line": 370, "column": 8}}, {"start": {"line": 370, "column": 8}, "end": {"line": 370, "column": 8}}]}, "31": {"line": 379, "type": "if", "locations": [{"start": {"line": 379, "column": 64}, "end": {"line": 379, "column": 64}}, {"start": {"line": 379, "column": 64}, "end": {"line": 379, "column": 64}}]}, "32": {"line": 380, "type": "if", "locations": [{"start": {"line": 380, "column": 8}, "end": {"line": 380, "column": 8}}, {"start": {"line": 380, "column": 8}, "end": {"line": 380, "column": 8}}]}, "33": {"line": 389, "type": "if", "locations": [{"start": {"line": 389, "column": 30}, "end": {"line": 389, "column": 30}}, {"start": {"line": 389, "column": 30}, "end": {"line": 389, "column": 30}}]}, "34": {"line": 396, "type": "if", "locations": [{"start": {"line": 396, "column": 32}, "end": {"line": 396, "column": 32}}, {"start": {"line": 396, "column": 32}, "end": {"line": 396, "column": 32}}]}, "35": {"line": 403, "type": "if", "locations": [{"start": {"line": 403, "column": 79}, "end": {"line": 403, "column": 79}}, {"start": {"line": 403, "column": 79}, "end": {"line": 403, "column": 79}}]}, "36": {"line": 404, "type": "if", "locations": [{"start": {"line": 404, "column": 8}, "end": {"line": 404, "column": 8}}, {"start": {"line": 404, "column": 8}, "end": {"line": 404, "column": 8}}]}, "37": {"line": 405, "type": "if", "locations": [{"start": {"line": 405, "column": 8}, "end": {"line": 405, "column": 8}}, {"start": {"line": 405, "column": 8}, "end": {"line": 405, "column": 8}}]}}}, "contracts/HAOXVestingV2FixedSecure.sol": {"l": {"76": 0, "77": 0, "81": 0, "82": 0, "83": 0, "84": 0, "97": 0, "98": 0, "100": 0, "101": 0, "108": 0, "109": 0, "111": 0, "112": 0, "119": 0, "120": 0, "122": 0, "123": 0, "130": 0, "131": 0, "133": 0, "134": 0, "136": 0, "145": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "163": 0, "164": 0, "165": 0, "168": 0, "169": 0, "172": 0, "182": 0, "185": 0, "195": 0, "196": 0, "199": 0, "200": 0, "202": 0, "211": 0, "222": 0, "224": 0, "225": 0, "227": 0, "243": 0, "246": 0, "252": 0, "258": 0, "261": 0, "262": 0, "263": 0, "266": 0, "267": 0, "270": 0, "272": 0, "283": 0, "286": 0, "291": 0, "292": 0, "294": 0, "301": 0, "319": 0, "321": 0, "322": 0, "323": 0, "324": 0, "325": 0, "329": 0, "347": 0, "359": 0, "360": 0, "363": 0, "364": 0, "367": 0, "372": 0, "379": 0, "380": 0, "381": 0, "382": 0, "383": 0, "392": 0, "399": 0, "400": 0, "402": 0, "407": 0, "408": 0, "410": 0, "424": 0, "425": 0, "432": 0, "434": 0, "435": 0, "438": 0, "439": 0, "442": 0, "443": 0, "446": 0, "447": 0, "450": 0, "463": 0, "464": 0, "465": 0, "466": 0, "469": 0, "470": 0, "471": 0, "472": 0, "473": 0, "474": 0, "475": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2FixedSecure.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0], "47": [0, 0], "48": [0, 0], "49": [0, 0], "50": [0, 0], "51": [0, 0], "52": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0}, "fnMap": {"1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 75, "loc": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 4}}}, "2": {"name": "validEmergencyRequest", "line": 80, "loc": {"start": {"line": 80, "column": 4}, "end": {"line": 85, "column": 4}}}, "3": {"name": "constructor", "line": 95, "loc": {"start": {"line": 90, "column": 4}, "end": {"line": 102, "column": 4}}}, "4": {"name": "addEmergencySigner", "line": 107, "loc": {"start": {"line": 107, "column": 4}, "end": {"line": 113, "column": 4}}}, "5": {"name": "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 118, "loc": {"start": {"line": 118, "column": 4}, "end": {"line": 124, "column": 4}}}, "6": {"name": "setRequiredSignatures", "line": 129, "loc": {"start": {"line": 129, "column": 4}, "end": {"line": 137, "column": 4}}}, "7": {"name": "getSignerCount", "line": 142, "loc": {"start": {"line": 142, "column": 4}, "end": {"line": 146, "column": 4}}}, "8": {"name": "requestEmergencyWithdraw", "line": 155, "loc": {"start": {"line": 151, "column": 4}, "end": {"line": 212, "column": 4}}}, "9": {"name": "approveEmergencyWithdraw", "line": 220, "loc": {"start": {"line": 217, "column": 4}, "end": {"line": 233, "column": 4}}}, "10": {"name": "executeEmergencyWithdraw", "line": 241, "loc": {"start": {"line": 238, "column": 4}, "end": {"line": 273, "column": 4}}}, "11": {"name": "cancelEmergencyWithdraw", "line": 281, "loc": {"start": {"line": 278, "column": 4}, "end": {"line": 295, "column": 4}}}, "12": {"name": "getPendingRequests", "line": 300, "loc": {"start": {"line": 300, "column": 4}, "end": {"line": 302, "column": 4}}}, "13": {"name": "getRequestDetails", "line": 307, "loc": {"start": {"line": 307, "column": 4}, "end": {"line": 341, "column": 4}}}, "14": {"name": "hasApproved", "line": 346, "loc": {"start": {"line": 346, "column": 4}, "end": {"line": 348, "column": 4}}}, "15": {"name": "getEmergencyStatistics", "line": 353, "loc": {"start": {"line": 353, "column": 4}, "end": {"line": 373, "column": 4}}}, "16": {"name": "_removePendingRequest", "line": 378, "loc": {"start": {"line": 378, "column": 4}, "end": {"line": 386, "column": 4}}}, "17": {"name": "emergencyWithdraw", "line": 391, "loc": {"start": {"line": 391, "column": 4}, "end": {"line": 393, "column": 4}}}, "18": {"name": "batchApproveRequests", "line": 398, "loc": {"start": {"line": 398, "column": 4}, "end": {"line": 418, "column": 4}}}, "19": {"name": "emergencyPause", "line": 423, "loc": {"start": {"line": 423, "column": 4}, "end": {"line": 426, "column": 4}}}, "20": {"name": "canExecuteRequest", "line": 431, "loc": {"start": {"line": 431, "column": 4}, "end": {"line": 451, "column": 4}}}, "21": {"name": "getSecurityStatus", "line": 456, "loc": {"start": {"line": 456, "column": 4}, "end": {"line": 479, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 71}}, "2": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 81}}, "3": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 82}}, "4": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 76}}, "5": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 45}}, "6": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 44}}, "7": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 62}}, "8": {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 66}}, "9": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 41}}, "10": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 65}}, "11": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 102}}, "12": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 43}}, "13": {"start": {"line": 130, "column": 8}, "end": {"line": 130, "column": 75}}, "14": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 89}}, "15": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 48}}, "16": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 62}}, "17": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 16}}, "18": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 60}}, "19": {"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 59}}, "20": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 78}}, "21": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 59}}, "22": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 62}}, "23": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 44}}, "24": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 64}}, "25": {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 63}}, "26": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 80}}, "27": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 80}}, "28": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 5344}}, "29": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 85}}, "30": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 38}}, "31": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 6214}}, "32": {"start": {"line": 211, "column": 8}, "end": {"line": 211, "column": 84}}, "33": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 78}}, "34": {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 6885}}, "35": {"start": {"line": 243, "column": 8}, "end": {"line": 243, "column": 71}}, "36": {"start": {"line": 246, "column": 8}, "end": {"line": 246, "column": 7348}}, "37": {"start": {"line": 252, "column": 8}, "end": {"line": 252, "column": 7509}}, "38": {"start": {"line": 258, "column": 8}, "end": {"line": 258, "column": 51}}, "39": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 44}}, "40": {"start": {"line": 262, "column": 8}, "end": {"line": 262, "column": 56}}, "41": {"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 65}}, "42": {"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 74}}, "43": {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 39}}, "44": {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 89}}, "45": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 71}}, "46": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 8550}}, "47": {"start": {"line": 292, "column": 8}, "end": {"line": 292, "column": 39}}, "48": {"start": {"line": 294, "column": 8}, "end": {"line": 294, "column": 70}}, "49": {"start": {"line": 301, "column": 8}, "end": {"line": 301, "column": 30}}, "50": {"start": {"line": 319, "column": 8}, "end": {"line": 319, "column": 70}}, "51": {"start": {"line": 321, "column": 8}, "end": {"line": 321, "column": 29}}, "52": {"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 9520}}, "53": {"start": {"line": 323, "column": 12}, "end": {"line": 323, "column": 67}}, "54": {"start": {"line": 324, "column": 12}, "end": {"line": 324, "column": 9679}}, "55": {"start": {"line": 329, "column": 8}, "end": {"line": 329, "column": 9805}}, "56": {"start": {"line": 347, "column": 8}, "end": {"line": 347, "column": 52}}, "57": {"start": {"line": 367, "column": 8}, "end": {"line": 367, "column": 10783}}, "58": {"start": {"line": 379, "column": 8}, "end": {"line": 379, "column": 11100}}, "59": {"start": {"line": 380, "column": 12}, "end": {"line": 380, "column": 11171}}, "60": {"start": {"line": 382, "column": 16}, "end": {"line": 382, "column": 36}}, "61": {"start": {"line": 392, "column": 8}, "end": {"line": 392, "column": 53}}, "62": {"start": {"line": 399, "column": 8}, "end": {"line": 399, "column": 11704}}, "63": {"start": {"line": 400, "column": 12}, "end": {"line": 400, "column": 45}}, "64": {"start": {"line": 402, "column": 12}, "end": {"line": 402, "column": 11830}}, "65": {"start": {"line": 410, "column": 16}, "end": {"line": 410, "column": 12228}}, "66": {"start": {"line": 424, "column": 8}, "end": {"line": 424, "column": 43}}, "67": {"start": {"line": 425, "column": 8}, "end": {"line": 425, "column": 15}}, "68": {"start": {"line": 432, "column": 8}, "end": {"line": 432, "column": 70}}, "69": {"start": {"line": 434, "column": 8}, "end": {"line": 434, "column": 12838}}, "70": {"start": {"line": 435, "column": 12}, "end": {"line": 435, "column": 24}}, "71": {"start": {"line": 438, "column": 8}, "end": {"line": 438, "column": 12964}}, "72": {"start": {"line": 439, "column": 12}, "end": {"line": 439, "column": 24}}, "73": {"start": {"line": 442, "column": 8}, "end": {"line": 442, "column": 13080}}, "74": {"start": {"line": 443, "column": 12}, "end": {"line": 443, "column": 24}}, "75": {"start": {"line": 446, "column": 8}, "end": {"line": 446, "column": 13186}}, "76": {"start": {"line": 447, "column": 12}, "end": {"line": 447, "column": 24}}, "77": {"start": {"line": 450, "column": 8}, "end": {"line": 450, "column": 19}}, "78": {"start": {"line": 470, "column": 8}, "end": {"line": 470, "column": 13787}}, "79": {"start": {"line": 472, "column": 12}, "end": {"line": 472, "column": 13923}}, "80": {"start": {"line": 473, "column": 16}, "end": {"line": 473, "column": 87}}, "81": {"start": {"line": 474, "column": 16}, "end": {"line": 474, "column": 14087}}}, "branchMap": {"1": {"line": 76, "type": "if", "locations": [{"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 8}}, {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 8}}]}, "2": {"line": 81, "type": "if", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 8}}, {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 8}}]}, "3": {"line": 82, "type": "if", "locations": [{"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 8}}, {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 8}}]}, "4": {"line": 83, "type": "if", "locations": [{"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 8}}, {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 8}}]}, "5": {"line": 107, "type": "if", "locations": [{"start": {"line": 107, "column": 57}, "end": {"line": 107, "column": 57}}, {"start": {"line": 107, "column": 57}, "end": {"line": 107, "column": 57}}]}, "6": {"line": 108, "type": "if", "locations": [{"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}, {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}]}, "7": {"line": 109, "type": "if", "locations": [{"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 8}}, {"start": {"line": 109, "column": 8}, "end": {"line": 109, "column": 8}}]}, "8": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 60}, "end": {"line": 118, "column": 60}}, {"start": {"line": 118, "column": 60}, "end": {"line": 118, "column": 60}}]}, "9": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 8}}, {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 8}}]}, "10": {"line": 120, "type": "if", "locations": [{"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 8}}, {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 8}}]}, "11": {"line": 129, "type": "if", "locations": [{"start": {"line": 129, "column": 63}, "end": {"line": 129, "column": 63}}, {"start": {"line": 129, "column": 63}, "end": {"line": 129, "column": 63}}]}, "12": {"line": 130, "type": "if", "locations": [{"start": {"line": 130, "column": 8}, "end": {"line": 130, "column": 8}}, {"start": {"line": 130, "column": 8}, "end": {"line": 130, "column": 8}}]}, "13": {"line": 131, "type": "if", "locations": [{"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 8}}, {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 8}}]}, "14": {"line": 155, "type": "if", "locations": [{"start": {"line": 155, "column": 15}, "end": {"line": 155, "column": 15}}, {"start": {"line": 155, "column": 15}, "end": {"line": 155, "column": 15}}]}, "15": {"line": 155, "type": "if", "locations": [{"start": {"line": 155, "column": 35}, "end": {"line": 155, "column": 35}}, {"start": {"line": 155, "column": 35}, "end": {"line": 155, "column": 35}}]}, "16": {"line": 156, "type": "if", "locations": [{"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 8}}, {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 8}}]}, "17": {"line": 157, "type": "if", "locations": [{"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 8}}, {"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 8}}]}, "18": {"line": 158, "type": "if", "locations": [{"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 8}}, {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 8}}]}, "19": {"line": 159, "type": "if", "locations": [{"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 8}}, {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 8}}]}, "20": {"line": 160, "type": "if", "locations": [{"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 8}}, {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 8}}]}, "21": {"line": 165, "type": "if", "locations": [{"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}, {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}]}, "22": {"line": 169, "type": "if", "locations": [{"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 8}}, {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 8}}]}, "23": {"line": 182, "type": "if", "locations": [{"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}, {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}]}, "24": {"line": 219, "type": "if", "locations": [{"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}, {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}]}, "25": {"line": 220, "type": "if", "locations": [{"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 8}}, {"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 8}}]}, "26": {"line": 222, "type": "if", "locations": [{"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 8}}, {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 8}}]}, "27": {"line": 240, "type": "if", "locations": [{"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 8}}, {"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 8}}]}, "28": {"line": 241, "type": "if", "locations": [{"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 8}}, {"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 8}}]}, "29": {"line": 246, "type": "if", "locations": [{"start": {"line": 246, "column": 8}, "end": {"line": 246, "column": 8}}, {"start": {"line": 246, "column": 8}, "end": {"line": 246, "column": 8}}]}, "30": {"line": 252, "type": "if", "locations": [{"start": {"line": 252, "column": 8}, "end": {"line": 252, "column": 8}}, {"start": {"line": 252, "column": 8}, "end": {"line": 252, "column": 8}}]}, "31": {"line": 258, "type": "if", "locations": [{"start": {"line": 258, "column": 8}, "end": {"line": 258, "column": 8}}, {"start": {"line": 258, "column": 8}, "end": {"line": 258, "column": 8}}]}, "32": {"line": 263, "type": "if", "locations": [{"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 8}}, {"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 8}}]}, "33": {"line": 267, "type": "if", "locations": [{"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 8}}, {"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 8}}]}, "34": {"line": 280, "type": "if", "locations": [{"start": {"line": 280, "column": 8}, "end": {"line": 280, "column": 8}}, {"start": {"line": 280, "column": 8}, "end": {"line": 280, "column": 8}}]}, "35": {"line": 281, "type": "if", "locations": [{"start": {"line": 281, "column": 8}, "end": {"line": 281, "column": 8}}, {"start": {"line": 281, "column": 8}, "end": {"line": 281, "column": 8}}]}, "36": {"line": 286, "type": "if", "locations": [{"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 8}}, {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 8}}]}, "37": {"line": 287, "type": "cond-expr", "locations": [{"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": 42}}, {"start": {"line": 287, "column": 47}, "end": {"line": 287, "column": 67}}]}, "38": {"line": 322, "type": "if", "locations": [{"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 8}}, {"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 8}}]}, "39": {"line": 324, "type": "if", "locations": [{"start": {"line": 324, "column": 12}, "end": {"line": 324, "column": 12}}, {"start": {"line": 324, "column": 12}, "end": {"line": 324, "column": 12}}]}, "40": {"line": 380, "type": "if", "locations": [{"start": {"line": 380, "column": 12}, "end": {"line": 380, "column": 12}}, {"start": {"line": 380, "column": 12}, "end": {"line": 380, "column": 12}}]}, "41": {"line": 398, "type": "if", "locations": [{"start": {"line": 398, "column": 74}, "end": {"line": 398, "column": 74}}, {"start": {"line": 398, "column": 74}, "end": {"line": 398, "column": 74}}]}, "42": {"line": 402, "type": "if", "locations": [{"start": {"line": 402, "column": 12}, "end": {"line": 402, "column": 12}}, {"start": {"line": 402, "column": 12}, "end": {"line": 402, "column": 12}}]}, "43": {"line": 423, "type": "if", "locations": [{"start": {"line": 423, "column": 39}, "end": {"line": 423, "column": 39}}, {"start": {"line": 423, "column": 39}, "end": {"line": 423, "column": 39}}]}, "44": {"line": 424, "type": "if", "locations": [{"start": {"line": 424, "column": 8}, "end": {"line": 424, "column": 8}}, {"start": {"line": 424, "column": 8}, "end": {"line": 424, "column": 8}}]}, "45": {"line": 434, "type": "if", "locations": [{"start": {"line": 434, "column": 8}, "end": {"line": 434, "column": 8}}, {"start": {"line": 434, "column": 8}, "end": {"line": 434, "column": 8}}]}, "46": {"line": 434, "type": "cond-expr", "locations": [{"start": {"line": 434, "column": 12}, "end": {"line": 434, "column": 35}}, {"start": {"line": 434, "column": 40}, "end": {"line": 434, "column": 55}}]}, "47": {"line": 434, "type": "cond-expr", "locations": [{"start": {"line": 434, "column": 12}, "end": {"line": 434, "column": 55}}, {"start": {"line": 434, "column": 60}, "end": {"line": 434, "column": 76}}]}, "48": {"line": 438, "type": "if", "locations": [{"start": {"line": 438, "column": 8}, "end": {"line": 438, "column": 8}}, {"start": {"line": 438, "column": 8}, "end": {"line": 438, "column": 8}}]}, "49": {"line": 442, "type": "if", "locations": [{"start": {"line": 442, "column": 8}, "end": {"line": 442, "column": 8}}, {"start": {"line": 442, "column": 8}, "end": {"line": 442, "column": 8}}]}, "50": {"line": 446, "type": "if", "locations": [{"start": {"line": 446, "column": 8}, "end": {"line": 446, "column": 8}}, {"start": {"line": 446, "column": 8}, "end": {"line": 446, "column": 8}}]}, "51": {"line": 470, "type": "if", "locations": [{"start": {"line": 470, "column": 8}, "end": {"line": 470, "column": 8}}, {"start": {"line": 470, "column": 8}, "end": {"line": 470, "column": 8}}]}, "52": {"line": 474, "type": "if", "locations": [{"start": {"line": 474, "column": 16}, "end": {"line": 474, "column": 16}}, {"start": {"line": 474, "column": 16}, "end": {"line": 474, "column": 16}}]}}}, "contracts/HAOXVestingV2Minimal.sol": {"l": {"79": 0, "80": 0, "81": 0, "82": 0, "84": 0, "85": 0, "86": 0, "87": 0, "90": 0, "93": 0, "101": 0, "111": 0, "112": 0, "120": 0, "122": 0, "123": 0, "127": 0, "128": 0, "129": 0, "132": 0, "133": 0, "138": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "146": 0, "147": 0, "148": 0, "152": 0, "153": 0, "154": 0, "155": 0, "164": 0, "165": 0, "166": 0, "169": 0, "170": 0, "173": 0, "174": 0, "176": 0, "179": 0, "186": 0, "189": 0, "190": 0, "197": 0, "198": 0, "200": 0, "202": 0, "209": 0, "216": 0, "217": 0, "218": 0, "219": 0, "220": 0, "222": 0, "223": 0, "225": 0, "238": 0, "239": 0, "241": 0, "242": 0, "243": 0, "244": 0, "246": 0, "247": 0, "248": 0, "263": 0, "264": 0, "266": 0, "279": 0, "286": 0, "290": 0, "294": 0, "298": 0, "305": 0, "306": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2Minimal.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "fnMap": {"1": {"name": "constructor", "line": 78, "loc": {"start": {"line": 73, "column": 4}, "end": {"line": 94, "column": 4}}}, "2": {"name": "_initializeRounds", "line": 99, "loc": {"start": {"line": 99, "column": 4}, "end": {"line": 114, "column": 4}}}, "3": {"name": "checkPriceCondition", "line": 119, "loc": {"start": {"line": 119, "column": 4}, "end": {"line": 158, "column": 4}}}, "4": {"name": "_unlockRound", "line": 163, "loc": {"start": {"line": 163, "column": 4}, "end": {"line": 180, "column": 4}}}, "5": {"name": "_getCurrentPrice", "line": 185, "loc": {"start": {"line": 185, "column": 4}, "end": {"line": 191, "column": 4}}}, "6": {"name": "requestEmergencyWithdraw", "line": 196, "loc": {"start": {"line": 196, "column": 4}, "end": {"line": 210, "column": 4}}}, "7": {"name": "executeEmergencyWithdraw", "line": 215, "loc": {"start": {"line": 215, "column": 4}, "end": {"line": 226, "column": 4}}}, "8": {"name": "getUnlockProgress", "line": 231, "loc": {"start": {"line": 231, "column": 4}, "end": {"line": 251, "column": 4}}}, "9": {"name": "getRoundInfo", "line": 256, "loc": {"start": {"line": 256, "column": 4}, "end": {"line": 273, "column": 4}}}, "10": {"name": "getPriceHistory", "line": 278, "loc": {"start": {"line": 278, "column": 4}, "end": {"line": 280, "column": 4}}}, "11": {"name": "addEmergencySigner", "line": 285, "loc": {"start": {"line": 285, "column": 4}, "end": {"line": 287, "column": 4}}}, "12": {"name": "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 289, "loc": {"start": {"line": 289, "column": 4}, "end": {"line": 291, "column": 4}}}, "13": {"name": "pause", "line": 293, "loc": {"start": {"line": 293, "column": 4}, "end": {"line": 295, "column": 4}}}, "14": {"name": "unpause", "line": 297, "loc": {"start": {"line": 297, "column": 4}, "end": {"line": 299, "column": 4}}}, "15": {"name": "emergencyPause", "line": 304, "loc": {"start": {"line": 304, "column": 4}, "end": {"line": 307, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 65}}, "2": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 68}}, "3": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 70}}, "4": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 74}}, "5": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 26}}, "6": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 3315}}, "7": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 4175}}, "8": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 42}}, "9": {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 4457}}, "10": {"start": {"line": 123, "column": 12}, "end": {"line": 123, "column": 18}}, "11": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 49}}, "12": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 49}}, "13": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 62}}, "14": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 47}}, "15": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 5064}}, "16": {"start": {"line": 141, "column": 12}, "end": {"line": 141, "column": 5100}}, "17": {"start": {"line": 144, "column": 16}, "end": {"line": 144, "column": 82}}, "18": {"start": {"line": 146, "column": 16}, "end": {"line": 146, "column": 85}}, "19": {"start": {"line": 147, "column": 16}, "end": {"line": 147, "column": 5458}}, "20": {"start": {"line": 148, "column": 20}, "end": {"line": 148, "column": 58}}, "21": {"start": {"line": 152, "column": 12}, "end": {"line": 152, "column": 5648}}, "22": {"start": {"line": 155, "column": 16}, "end": {"line": 155, "column": 84}}, "23": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 49}}, "24": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 49}}, "25": {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 51}}, "26": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 91}}, "27": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 97}}, "28": {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 102}}, "29": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 6840}}, "30": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 67}}, "31": {"start": {"line": 190, "column": 8}, "end": {"line": 190, "column": 42}}, "32": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 62}}, "33": {"start": {"line": 198, "column": 8}, "end": {"line": 198, "column": 66}}, "34": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 92}}, "35": {"start": {"line": 209, "column": 8}, "end": {"line": 209, "column": 75}}, "36": {"start": {"line": 216, "column": 8}, "end": {"line": 216, "column": 71}}, "37": {"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 60}}, "38": {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 53}}, "39": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 92}}, "40": {"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 62}}, "41": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 78}}, "42": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 65}}, "43": {"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 8770}}, "44": {"start": {"line": 242, "column": 12}, "end": {"line": 242, "column": 50}}, "45": {"start": {"line": 246, "column": 12}, "end": {"line": 246, "column": 8982}}, "46": {"start": {"line": 247, "column": 16}, "end": {"line": 247, "column": 74}}, "47": {"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 79}}, "48": {"start": {"line": 264, "column": 8}, "end": {"line": 264, "column": 48}}, "49": {"start": {"line": 266, "column": 8}, "end": {"line": 266, "column": 9653}}, "50": {"start": {"line": 279, "column": 8}, "end": {"line": 279, "column": 40}}, "51": {"start": {"line": 294, "column": 8}, "end": {"line": 294, "column": 15}}, "52": {"start": {"line": 298, "column": 8}, "end": {"line": 298, "column": 17}}, "53": {"start": {"line": 305, "column": 8}, "end": {"line": 305, "column": 62}}, "54": {"start": {"line": 306, "column": 8}, "end": {"line": 306, "column": 15}}}, "branchMap": {"1": {"line": 79, "type": "if", "locations": [{"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 8}}, {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 8}}]}, "2": {"line": 80, "type": "if", "locations": [{"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 8}}, {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 8}}]}, "3": {"line": 81, "type": "if", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 8}}, {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 8}}]}, "4": {"line": 82, "type": "if", "locations": [{"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 8}}, {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 8}}]}, "5": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 44}, "end": {"line": 119, "column": 44}}, {"start": {"line": 119, "column": 44}, "end": {"line": 119, "column": 44}}]}, "6": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 57}, "end": {"line": 119, "column": 57}}, {"start": {"line": 119, "column": 57}, "end": {"line": 119, "column": 57}}]}, "7": {"line": 122, "type": "if", "locations": [{"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 8}}, {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 8}}]}, "8": {"line": 140, "type": "if", "locations": [{"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 8}}, {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 8}}]}, "9": {"line": 141, "type": "if", "locations": [{"start": {"line": 141, "column": 12}, "end": {"line": 141, "column": 12}}, {"start": {"line": 141, "column": 12}, "end": {"line": 141, "column": 12}}]}, "10": {"line": 147, "type": "if", "locations": [{"start": {"line": 147, "column": 16}, "end": {"line": 147, "column": 16}}, {"start": {"line": 147, "column": 16}, "end": {"line": 147, "column": 16}}]}, "11": {"line": 152, "type": "if", "locations": [{"start": {"line": 152, "column": 12}, "end": {"line": 152, "column": 12}}, {"start": {"line": 152, "column": 12}, "end": {"line": 152, "column": 12}}]}, "12": {"line": 173, "type": "if", "locations": [{"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 8}}, {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 8}}]}, "13": {"line": 174, "type": "if", "locations": [{"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 8}}, {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 8}}]}, "14": {"line": 189, "type": "if", "locations": [{"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}, {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}]}, "15": {"line": 196, "type": "if", "locations": [{"start": {"line": 196, "column": 63}, "end": {"line": 196, "column": 63}}, {"start": {"line": 196, "column": 63}, "end": {"line": 196, "column": 63}}]}, "16": {"line": 197, "type": "if", "locations": [{"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 8}}, {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 8}}]}, "17": {"line": 198, "type": "if", "locations": [{"start": {"line": 198, "column": 8}, "end": {"line": 198, "column": 8}}, {"start": {"line": 198, "column": 8}, "end": {"line": 198, "column": 8}}]}, "18": {"line": 217, "type": "if", "locations": [{"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 8}}, {"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 8}}]}, "19": {"line": 218, "type": "if", "locations": [{"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 8}}, {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 8}}]}, "20": {"line": 219, "type": "if", "locations": [{"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}, {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}]}, "21": {"line": 220, "type": "if", "locations": [{"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 8}}, {"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 8}}]}, "22": {"line": 223, "type": "if", "locations": [{"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 8}}, {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 8}}]}, "23": {"line": 241, "type": "if", "locations": [{"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 8}}, {"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 8}}]}, "24": {"line": 246, "type": "if", "locations": [{"start": {"line": 246, "column": 12}, "end": {"line": 246, "column": 12}}, {"start": {"line": 246, "column": 12}, "end": {"line": 246, "column": 12}}]}, "25": {"line": 248, "type": "if", "locations": [{"start": {"line": 248, "column": 69}, "end": {"line": 248, "column": 69}}, {"start": {"line": 248, "column": 73}, "end": {"line": 248, "column": 105}}]}, "26": {"line": 263, "type": "if", "locations": [{"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 8}}, {"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 8}}]}, "27": {"line": 285, "type": "if", "locations": [{"start": {"line": 285, "column": 57}, "end": {"line": 285, "column": 57}}, {"start": {"line": 285, "column": 57}, "end": {"line": 285, "column": 57}}]}, "28": {"line": 289, "type": "if", "locations": [{"start": {"line": 289, "column": 60}, "end": {"line": 289, "column": 60}}, {"start": {"line": 289, "column": 60}, "end": {"line": 289, "column": 60}}]}, "29": {"line": 293, "type": "if", "locations": [{"start": {"line": 293, "column": 30}, "end": {"line": 293, "column": 30}}, {"start": {"line": 293, "column": 30}, "end": {"line": 293, "column": 30}}]}, "30": {"line": 297, "type": "if", "locations": [{"start": {"line": 297, "column": 32}, "end": {"line": 297, "column": 32}}, {"start": {"line": 297, "column": 32}, "end": {"line": 297, "column": 32}}]}, "31": {"line": 305, "type": "if", "locations": [{"start": {"line": 305, "column": 8}, "end": {"line": 305, "column": 8}}, {"start": {"line": 305, "column": 8}, "end": {"line": 305, "column": 8}}]}}}, "contracts/HAOXVestingV2Ultra.sol": {"l": {"64": 0, "65": 0, "66": 0, "67": 0, "69": 0, "70": 0, "71": 0, "72": 0, "74": 0, "77": 0, "84": 0, "90": 0, "91": 0, "104": 0, "105": 0, "107": 0, "108": 0, "110": 0, "111": 0, "113": 0, "114": 0, "115": 0, "116": 0, "118": 0, "119": 0, "123": 0, "125": 0, "133": 0, "134": 0, "136": 0, "138": 0, "139": 0, "147": 0, "150": 0, "151": 0, "161": 0, "163": 0, "167": 0, "173": 0, "174": 0, "184": 0, "186": 0, "187": 0, "188": 0, "193": 0, "195": 0, "196": 0, "198": 0, "201": 0, "208": 0, "215": 0, "216": 0, "223": 0, "227": 0, "239": 0, "240": 0, "256": 0, "257": 0, "258": 0, "261": 0, "268": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2Ultra.sol", "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0}, "b": {"1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0]}, "f": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "fnMap": {"1": {"name": "constructor", "line": 63, "loc": {"start": {"line": 58, "column": 4}, "end": {"line": 78, "column": 4}}}, "2": {"name": "_initializeRounds", "line": 83, "loc": {"start": {"line": 83, "column": 4}, "end": {"line": 98, "column": 4}}}, "3": {"name": "checkPriceCondition", "line": 103, "loc": {"start": {"line": 103, "column": 4}, "end": {"line": 127, "column": 4}}}, "4": {"name": "_unlockRound", "line": 132, "loc": {"start": {"line": 132, "column": 4}, "end": {"line": 141, "column": 4}}}, "5": {"name": "_getCurrentPrice", "line": 146, "loc": {"start": {"line": 146, "column": 4}, "end": {"line": 152, "column": 4}}}, "6": {"name": "requestEmergencyWithdraw", "line": 160, "loc": {"start": {"line": 157, "column": 4}, "end": {"line": 175, "column": 4}}}, "7": {"name": "executeEmergencyWithdraw", "line": 183, "loc": {"start": {"line": 180, "column": 4}, "end": {"line": 202, "column": 4}}}, "8": {"name": "setEmergency<PERSON><PERSON>er", "line": 207, "loc": {"start": {"line": 207, "column": 4}, "end": {"line": 209, "column": 4}}}, "9": {"name": "setRequiredSignatures", "line": 214, "loc": {"start": {"line": 214, "column": 4}, "end": {"line": 217, "column": 4}}}, "10": {"name": "pause", "line": 222, "loc": {"start": {"line": 222, "column": 4}, "end": {"line": 224, "column": 4}}}, "11": {"name": "unpause", "line": 226, "loc": {"start": {"line": 226, "column": 4}, "end": {"line": 228, "column": 4}}}, "12": {"name": "getRoundInfo", "line": 233, "loc": {"start": {"line": 233, "column": 4}, "end": {"line": 246, "column": 4}}}, "13": {"name": "getUnlockProgress", "line": 251, "loc": {"start": {"line": 251, "column": 4}, "end": {"line": 262, "column": 4}}}, "14": {"name": "getCurrentPrice", "line": 267, "loc": {"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 65}}, "2": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 68}}, "3": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 70}}, "4": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 74}}, "5": {"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 26}}, "6": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 2728}}, "7": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 3077}}, "8": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 42}}, "9": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 46}}, "10": {"start": {"line": 105, "column": 40}, "end": {"line": 105, "column": 46}}, "11": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 49}}, "12": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 34}}, "13": {"start": {"line": 108, "column": 28}, "end": {"line": 108, "column": 34}}, "14": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 49}}, "15": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 62}}, "16": {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 3786}}, "17": {"start": {"line": 116, "column": 12}, "end": {"line": 116, "column": 61}}, "18": {"start": {"line": 117, "column": 15}, "end": {"line": 117, "column": 4018}}, "19": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 4198}}, "20": {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 36}}, "21": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 49}}, "22": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 56}}, "23": {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 4630}}, "24": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 4839}}, "25": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 72}}, "26": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 42}}, "27": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 72}}, "28": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 5354}}, "29": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 58}}, "30": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 24}}, "31": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 69}}, "32": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 71}}, "33": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 53}}, "34": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 6138}}, "35": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 6323}}, "36": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 60}}, "37": {"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 64}}, "38": {"start": {"line": 201, "column": 8}, "end": {"line": 201, "column": 65}}, "39": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 56}}, "40": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 15}}, "41": {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 17}}, "42": {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 48}}, "43": {"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 7469}}, "44": {"start": {"line": 256, "column": 8}, "end": {"line": 256, "column": 28}}, "45": {"start": {"line": 257, "column": 8}, "end": {"line": 257, "column": 7865}}, "46": {"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 46}}, "47": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 53}}, "48": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 33}}}, "branchMap": {"1": {"line": 64, "type": "if", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}, {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}]}, "2": {"line": 65, "type": "if", "locations": [{"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 8}}, {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 8}}]}, "3": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}, {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}]}, "4": {"line": 67, "type": "if", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 8}}, {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 8}}]}, "5": {"line": 103, "type": "if", "locations": [{"start": {"line": 103, "column": 44}, "end": {"line": 103, "column": 44}}, {"start": {"line": 103, "column": 44}, "end": {"line": 103, "column": 44}}]}, "6": {"line": 105, "type": "if", "locations": [{"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}, {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}]}, "7": {"line": 108, "type": "if", "locations": [{"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}, {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}]}, "8": {"line": 113, "type": "if", "locations": [{"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 8}}, {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 8}}]}, "9": {"line": 117, "type": "if", "locations": [{"start": {"line": 117, "column": 15}, "end": {"line": 117, "column": 15}}, {"start": {"line": 117, "column": 15}, "end": {"line": 117, "column": 15}}]}, "10": {"line": 123, "type": "if", "locations": [{"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 8}}, {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 8}}]}, "11": {"line": 138, "type": "if", "locations": [{"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 8}}, {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 8}}]}, "12": {"line": 150, "type": "if", "locations": [{"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}, {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}]}, "13": {"line": 160, "type": "if", "locations": [{"start": {"line": 160, "column": 15}, "end": {"line": 160, "column": 15}}, {"start": {"line": 160, "column": 15}, "end": {"line": 160, "column": 15}}]}, "14": {"line": 161, "type": "if", "locations": [{"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 8}}, {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 8}}]}, "15": {"line": 183, "type": "if", "locations": [{"start": {"line": 183, "column": 15}, "end": {"line": 183, "column": 15}}, {"start": {"line": 183, "column": 15}, "end": {"line": 183, "column": 15}}]}, "16": {"line": 184, "type": "if", "locations": [{"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 8}}, {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 8}}]}, "17": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}, {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}]}, "18": {"line": 188, "type": "if", "locations": [{"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}, {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}]}, "19": {"line": 195, "type": "if", "locations": [{"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}, {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}]}, "20": {"line": 207, "type": "if", "locations": [{"start": {"line": 207, "column": 70}, "end": {"line": 207, "column": 70}}, {"start": {"line": 207, "column": 70}, "end": {"line": 207, "column": 70}}]}, "21": {"line": 214, "type": "if", "locations": [{"start": {"line": 214, "column": 63}, "end": {"line": 214, "column": 63}}, {"start": {"line": 214, "column": 63}, "end": {"line": 214, "column": 63}}]}, "22": {"line": 215, "type": "if", "locations": [{"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 8}}, {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 8}}]}, "23": {"line": 222, "type": "if", "locations": [{"start": {"line": 222, "column": 30}, "end": {"line": 222, "column": 30}}, {"start": {"line": 222, "column": 30}, "end": {"line": 222, "column": 30}}]}, "24": {"line": 226, "type": "if", "locations": [{"start": {"line": 226, "column": 32}, "end": {"line": 226, "column": 32}}, {"start": {"line": 226, "column": 32}, "end": {"line": 226, "column": 32}}]}, "25": {"line": 258, "type": "if", "locations": [{"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 12}}, {"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 12}}]}}}}