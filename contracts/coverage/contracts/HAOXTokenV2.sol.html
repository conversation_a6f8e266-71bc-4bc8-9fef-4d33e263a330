<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXTokenV2.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXTokenV2.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">16.33% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>8/49</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">13.79% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>8/58</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">41.67% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>5/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">15.38% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>10/65</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">77×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">80×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
&nbsp;
/**
 * @title HAOXTokenV2
 * @dev 重构的HAOX代币合约
 * 
 * 特点：
 * - 移除铸币权限
 * - 移除余额修改权限
 * - 移除强制转账权限
 * - 保留紧急暂停权限（72小时限制）
 * - 保留参数更新权限（24小时时间锁）
 * - 总供应量：5,000,000,000 HAOX
 * - 初始解锁：500,000,000 HAOX（10%）
 */
contract HAOXTokenV2 is ERC20, Pausable, Ownable {
    
    // 供应量分配
    uint256 public constant TOTAL_SUPPLY = 5_000_000_000 * 10**18; // 50亿HAOX
    uint256 public constant INITIAL_UNLOCK = 500_000_000 * 10**18; // 5亿HAOX（10%）
    uint256 public constant PRESALE_ALLOCATION = 200_000_000 * 10**18; // 2亿HAOX
    uint256 public constant PROJECT_RESERVE = 300_000_000 * 10**18; // 3亿HAOX（邀请奖励等）
    uint256 public constant LOCKED_SUPPLY = 4_500_000_000 * 10**18; // 45亿HAOX待解锁
    
    // 合约地址
    address public presaleContract;
    address public invitationContract;
    address public vestingContract;
    address public priceOracle;
    
    // 时间锁机制
    struct TimeLock {
        uint256 unlockTime;
        bool executed;
    }
    
    mapping(bytes32 =&gt; TimeLock) public timeLocks;
    uint256 public constant TIMELOCK_DELAY = 24 hours;
    uint256 public constant MAX_PAUSE_DURATION = 72 hours;
    
    // 暂停相关
    uint256 public pauseStartTime;
    
    // 事件
    event ContractAddressUpdated(string contractType, address oldAddress, address newAddress);
    event TimeLockCreated(bytes32 indexed lockId, uint256 unlockTime);
    event TimeLockExecuted(bytes32 indexed lockId);
    event EmergencyPauseActivated(uint256 duration);
    
    constructor() ERC20("HAOX Token", "HAOX") Ownable(msg.sender) {
        // 铸造初始解锁的代币到合约部署者
        _mint(msg.sender, INITIAL_UNLOCK);
    }
    
    /**
     * @dev 设置预售合约地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setPresaleContract(address _presaleContract) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setPresaleContract", _presaleContract, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            // 创建时间锁
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
            // 执行时间锁
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = presaleContract;</span>
            presaleContract = _presaleContract;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("presale", oldAddress, _presaleContract);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 设置邀请合约地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setInvitationContract(address _invitationContract) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setInvitationContract", _invitationContract, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = invitationContract;</span>
            invitationContract = _invitationContract;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("invitation", oldAddress, _invitationContract);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 设置解锁合约地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setVestingContract(address _vestingContract) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setVestingContract", _vestingContract, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = vestingContract;</span>
            vestingContract = _vestingContract;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("vesting", oldAddress, _vestingContract);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 设置价格预言机地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setPriceOracle(address _priceOracle) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setPriceOracle", _priceOracle, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = priceOracle;</span>
            priceOracle = _priceOracle;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("priceOracle", oldAddress, _priceOracle);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 紧急暂停（最长72小时）
     */
    function emergencyPause() external onlyOwner {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(!paused(), "Already paused");
        pauseStartTime = block.timestamp;
        _pause();
        emit EmergencyPauseActivated(MAX_PAUSE_DURATION);
    }
    
    /**
     * @dev 恢复运行
     */
    function unpause() external <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(paused(), "Not paused");
        _unpause();
        pauseStartTime = 0;
    }
    
    /**
     * @dev 自动解除暂停（72小时后任何人都可以调用）
     */
<span class="fstat-no" title="function not covered" >    function autoUnpause() external {</span>
<span class="cstat-no" title="statement not covered" >        require(paused(), "Not paused")</span>;
<span class="cstat-no" title="statement not covered" >        require(pauseStartTime &gt; 0, "Invalid pause state")</span>;
<span class="cstat-no" title="statement not covered" >        require(block.timestamp &gt;= pauseStartTime + MAX_PAUSE_DURATION, "Pause duration not exceeded")</span>;
        
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
        pauseStartTime = 0;
    }
    
    /**
     * @dev 放弃所有权（不可逆）
     */
<span class="fstat-no" title="function not covered" >    function renounceOwnership() public override onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        super.renounceOwnership()</span>;
    }
    
    /**
     * @dev 重写转账函数以支持暂停机制
     */
    function transfer(address to, uint256 amount) public override whenNotPaused returns (bool) {
        return super.transfer(to, amount);
    }
    
    /**
     * @dev 重写授权转账函数以支持暂停机制
     */
    function transferFrom(address from, address to, uint256 amount) public override <span class="missing-if-branch" title="else path not taken" >E</span>whenNotPaused returns (bool) {
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev 获取合约状态
     */
<span class="fstat-no" title="function not covered" >    function getContractStatus() external view returns (</span>
        bool isPaused,
        uint256 pauseDuration,
        uint256 remainingPauseTime,
        address presale,
        address invitation,
        address vesting,
        address oracle
    ) {
<span class="cstat-no" title="statement not covered" >        uint256 remaining = 0;</span>
<span class="cstat-no" title="statement not covered" >        if (paused() &amp;&amp; pauseStartTime &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            uint256 elapsed = block.timestamp - pauseStartTime;</span>
            remaining = elapsed &lt; MAX_PAUSE_DURATION ? MAX_PAUSE_DURATION - elapsed : 0;
        }
        
<span class="cstat-no" title="statement not covered" >        return (</span>
            paused(),
            pauseStartTime &gt; 0 ? block.timestamp - pauseStartTime : 0,
            remaining,
            presaleContract,
            invitationContract,
            vestingContract,
            priceOracle
        );
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 21:36:12 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
