<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXPriceOracleV2.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXPriceOracleV2.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/40</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/54</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/66</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
&nbsp;
// Chainlink价格接口
interface AggregatorV3Interface {
    function latestRoundData() external view returns (
        uint80 roundId,
        int256 price,
        uint256 startedAt,
        uint256 updatedAt,
        uint80 answeredInRound
    );
    function decimals() external view returns (uint8);
}
&nbsp;
// PancakeSwap接口（简化）
interface IPancakeSwapPair {
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function token0() external view returns (address);
    function token1() external view returns (address);
}
&nbsp;
/**
 * @title HAOXPriceOracleV2
 * @dev HAOX价格预言机合约
 *
 * 功能：
 * - 集成Chainlink获取BNB/USD价格
 * - 集成PancakeSwap TWAP作为备用
 * - 多源价格验证（偏差不超过5%）
 * - 每小时自动更新
 * - 异常处理机制
 */
contract HAOXPriceOracleV2 is Ownable, Pausable {
    
    // 价格数据结构
    struct PriceData {
        uint256 price; // 价格（8位小数）
        uint256 timestamp;
        uint256 confidence; // 置信度（0-100）
        bool isValid;
    }
    
    // 配置参数
    uint256 public constant MAX_PRICE_DEVIATION = 500; // 5% (basis points)
    uint256 public constant UPDATE_INTERVAL = 1 hours;
    uint256 public constant PRICE_STALENESS_THRESHOLD = 2 hours;
    uint256 public constant MIN_CONFIDENCE = 80; // 最低置信度80%
    
    // 价格源
    AggregatorV3Interface public bnbUsdChainlink;
    IPancakeSwapPair public haoxBnbPair;
    address public haoxToken;
    address public wbnb;
    
    // 价格数据
    PriceData public currentHaoxPrice;
    PriceData public currentBnbPrice;
    mapping(uint256 =&gt; PriceData) public priceHistory; // timestamp =&gt; price
    
    // 状态变量
    uint256 public lastUpdateTime;
    bool public emergencyMode;
    uint256 public emergencyPrice; // 紧急情况下的固定价格
    
    // 事件
    event PriceUpdated(
        uint256 haoxPrice,
        uint256 bnbPrice,
        uint256 confidence,
        string source
    );
    event EmergencyModeActivated(uint256 fixedPrice);
    event EmergencyModeDeactivated();
    event PriceSourceUpdated(string sourceType, address newAddress);
    
<span class="fstat-no" title="function not covered" >    constructor(</span>
        address _bnbUsdChainlink,
        address _haoxBnbPair,
        address _haoxToken,
        address _wbnb
    ) Ownable(msg.sender) {
<span class="cstat-no" title="statement not covered" >        require(_bnbUsdChainlink != address(0), "Invalid Chainlink address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_haoxBnbPair != address(0), "Invalid pair address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_haoxToken != address(0), "Invalid HAOX address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_wbnb != address(0), "Invalid WBNB address")</span>;
        
        bnbUsdChainlink = AggregatorV3Interface(_bnbUsdChainlink);
        haoxBnbPair = IPancakeSwapPair(_haoxBnbPair);
        haoxToken = _haoxToken;
        wbnb = _wbnb;
        
        // 初始化价格
<span class="cstat-no" title="statement not covered" >        _updatePrices()</span>;
    }
    
    /**
     * @dev 获取BNB/USD价格（从Chainlink）
     */
<span class="fstat-no" title="function not covered" >    function getBnbUsdPrice() public view returns (uint256 price, uint256 confidence) {</span>
<span class="cstat-no" title="statement not covered" >        try bnbUsdChainlink.latestRoundData() returns (</span>
            uint80,
            int256 _price,
            uint256,
            uint256 updatedAt,
            uint80
        ) {
<span class="cstat-no" title="statement not covered" >            require(_price &gt; 0, "Invalid price")</span>;
<span class="cstat-no" title="statement not covered" >            require(block.timestamp - updatedAt &lt;= PRICE_STALENESS_THRESHOLD, "Price too stale")</span>;
            
<span class="cstat-no" title="statement not covered" >            uint8 decimals = bnbUsdChainlink.decimals();</span>
            price = uint256(_price) * 10**(8 - decimals); // 标准化到8位小数
            
            // 计算置信度（基于数据新鲜度）
<span class="cstat-no" title="statement not covered" >            uint256 age = block.timestamp - updatedAt;</span>
            confidence = age &lt; UPDATE_INTERVAL ? 100 : (100 * UPDATE_INTERVAL) / age;
            confidence = confidence &gt; 100 ? 100 : confidence;
            
        } catch {
            price = 0;
            confidence = 0;
        }
    }
    
    /**
     * @dev 获取HAOX/BNB价格（从PancakeSwap）
     */
<span class="fstat-no" title="function not covered" >    function getHaoxBnbPrice() public view returns (uint256 price, uint256 confidence) {</span>
<span class="cstat-no" title="statement not covered" >        try haoxBnbPair.getReserves() returns (</span>
            uint112 reserve0,
            uint112 reserve1,
            uint32 blockTimestampLast
        ) {
<span class="cstat-no" title="statement not covered" >            require(reserve0 &gt; 0 &amp;&amp; reserve1 &gt; 0, "Invalid reserves")</span>;
            
            // 确定哪个是HAOX，哪个是BNB
<span class="cstat-no" title="statement not covered" >            bool haoxIsToken0 = haoxBnbPair.token0() == haoxToken;</span>
            
<span class="cstat-no" title="statement not covered" >            uint256 haoxReserve = haoxIsToken0 ? uint256(reserve0) : uint256(reserve1);</span>
<span class="cstat-no" title="statement not covered" >            uint256 bnbReserve = haoxIsToken0 ? uint256(reserve1) : uint256(reserve0);</span>
            
            // 计算价格：1 HAOX = ? BNB
            price = (bnbReserve * 10**8) / haoxReserve; // 8位小数
            
            // 计算置信度（基于流动性和时间）
<span class="cstat-no" title="statement not covered" >            uint256 totalLiquidity = haoxReserve + bnbReserve;</span>
<span class="cstat-no" title="statement not covered" >            uint256 age = block.timestamp - uint256(blockTimestampLast);</span>
            
            confidence = totalLiquidity &gt; 1000000 * 10**18 ? 90 : 70; // 基于流动性
<span class="cstat-no" title="statement not covered" >            if (age &gt; UPDATE_INTERVAL) {</span>
                confidence = (confidence * UPDATE_INTERVAL) / age;
            }
            confidence = confidence &gt; 100 ? 100 : confidence;
            
        } catch {
            price = 0;
            confidence = 0;
        }
    }
    
    /**
     * @dev 计算HAOX/USD价格
     */
<span class="fstat-no" title="function not covered" >    function getHaoxUsdPrice() public view returns (uint256 price, uint256 confidence) {</span>
<span class="cstat-no" title="statement not covered" >        if (emergencyMode) {</span>
<span class="cstat-no" title="statement not covered" >            return (emergencyPrice, 100);</span>
        }
        
<span class="cstat-no" title="statement not covered" >        (uint256 bnbUsdPrice, uint256 bnbConfidence) = getBnbUsdPrice();</span>
<span class="cstat-no" title="statement not covered" >        (uint256 haoxBnbPrice, uint256 haoxConfidence) = getHaoxBnbPrice();</span>
        
<span class="cstat-no" title="statement not covered" >        if (bnbUsdPrice == 0 || haoxBnbPrice == 0) {</span>
<span class="cstat-no" title="statement not covered" >            return (currentHaoxPrice.price, currentHaoxPrice.confidence);</span>
        }
        
        // HAOX/USD = HAOX/BNB * BNB/USD
        price = (haoxBnbPrice * bnbUsdPrice) / 10**8;
        confidence = (bnbConfidence * haoxConfidence) / 100;
    }
    
    /**
     * @dev 更新价格（公开函数，任何人都可以调用）
     */
<span class="fstat-no" title="function not covered" >    function updatePrices() external whenNotPaused {</span>
<span class="cstat-no" title="statement not covered" >        require(</span>
            block.timestamp &gt;= lastUpdateTime + UPDATE_INTERVAL,
            "Update too frequent"
        );
<span class="cstat-no" title="statement not covered" >        _updatePrices()</span>;
    }
    
    /**
     * @dev 内部价格更新逻辑
     */
<span class="fstat-no" title="function not covered" >    function _updatePrices() internal {</span>
<span class="cstat-no" title="statement not covered" >        (uint256 haoxPrice, uint256 haoxConfidence) = getHaoxUsdPrice();</span>
<span class="cstat-no" title="statement not covered" >        (uint256 bnbPrice, uint256 bnbConfidence) = getBnbUsdPrice();</span>
        
        // 验证价格有效性
<span class="cstat-no" title="statement not covered" >        if (haoxConfidence &gt;= MIN_CONFIDENCE &amp;&amp; bnbConfidence &gt;= MIN_CONFIDENCE) {</span>
            // 检查价格偏差
<span class="cstat-no" title="statement not covered" >            if (currentHaoxPrice.isValid) {</span>
<span class="cstat-no" title="statement not covered" >                uint256 deviation = haoxPrice &gt; currentHaoxPrice.price</span>
                    ? ((haoxPrice - currentHaoxPrice.price) * 10000) / currentHaoxPrice.price
                    : ((currentHaoxPrice.price - haoxPrice) * 10000) / currentHaoxPrice.price;
                
<span class="cstat-no" title="statement not covered" >                if (deviation &gt; MAX_PRICE_DEVIATION) {</span>
                    // 价格偏差过大，降低置信度
                    haoxConfidence = haoxConfidence / 2;
                }
            }
            
            // 更新价格
            currentHaoxPrice = PriceData({
                price: haoxPrice,
                timestamp: block.timestamp,
                confidence: haoxConfidence,
                isValid: true
            });
            
            currentBnbPrice = PriceData({
                price: bnbPrice,
                timestamp: block.timestamp,
                confidence: bnbConfidence,
                isValid: true
            });
            
            // 保存历史价格
            priceHistory[block.timestamp] = currentHaoxPrice;
            lastUpdateTime = block.timestamp;
            
<span class="cstat-no" title="statement not covered" >            emit PriceUpdated(haoxPrice, bnbPrice, haoxConfidence, "chainlink_pancakeswap");</span>
        }
    }
    
    /**
     * @dev 激活紧急模式（管理员）
     */
<span class="fstat-no" title="function not covered" >    function activateEmergencyMode(uint256 _fixedPrice) external onlyOwner {</span>
        emergencyMode = true;
        emergencyPrice = _fixedPrice;
<span class="cstat-no" title="statement not covered" >        emit EmergencyModeActivated(_fixedPrice);</span>
    }
    
    /**
     * @dev 停用紧急模式（管理员）
     */
<span class="fstat-no" title="function not covered" >    function deactivateEmergencyMode() external onlyOwner {</span>
        emergencyMode = false;
        emergencyPrice = 0;
<span class="cstat-no" title="statement not covered" >        emit EmergencyModeDeactivated();</span>
    }
    
    /**
     * @dev 获取最新价格（外部调用接口）
     */
<span class="fstat-no" title="function not covered" >    function getLatestPrice() external view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        (uint256 price,) = getHaoxUsdPrice();</span>
<span class="cstat-no" title="statement not covered" >        return price;</span>
    }
    
    /**
     * @dev 获取价格和置信度
     */
<span class="fstat-no" title="function not covered" >    function getPriceWithConfidence() external view returns (uint256 price, uint256 confidence) {</span>
<span class="cstat-no" title="statement not covered" >        return getHaoxUsdPrice();</span>
    }
    
    /**
     * @dev 暂停合约
     */
<span class="fstat-no" title="function not covered" >    function pause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
    
    /**
     * @dev 恢复合约
     */
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 12:47:30 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
