<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> contracts/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">37.74% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>137/363</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">25.24% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>106/420</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">41.58% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>42/101</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">38.67% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>203/525</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="HAOXInvitationV2.sol"><a href="HAOXInvitationV2.sol.html">HAOXInvitationV2.sol</a></td>
	<td data-value="1.82" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.82" class="pct low">1.82%</td>
	<td data-value="55" class="abs low">1/55</td>
	<td data-value="1.56" class="pct low">1.56%</td>
	<td data-value="64" class="abs low">1/64</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="13" class="abs low">1/13</td>
	<td data-value="2.53" class="pct low">2.53%</td>
	<td data-value="79" class="abs low">2/79</td>
	</tr>

<tr>
	<td class="file medium" data-value="HAOXPresaleV2.sol"><a href="HAOXPresaleV2.sol.html">HAOXPresaleV2.sol</a></td>
	<td data-value="76" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 76%;"></div><div class="cover-empty" style="width:24%;"></div></div></td>
	<td data-value="76" class="pct medium">76%</td>
	<td data-value="50" class="abs medium">38/50</td>
	<td data-value="46.3" class="pct low">46.3%</td>
	<td data-value="54" class="abs low">25/54</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="12" class="abs medium">9/12</td>
	<td data-value="74.07" class="pct medium">74.07%</td>
	<td data-value="81" class="abs medium">60/81</td>
	</tr>

<tr>
	<td class="file medium" data-value="HAOXPriceAggregatorMinimal.sol"><a href="HAOXPriceAggregatorMinimal.sol.html">HAOXPriceAggregatorMinimal.sol</a></td>
	<td data-value="69.84" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 69%;"></div><div class="cover-empty" style="width:31%;"></div></div></td>
	<td data-value="69.84" class="pct medium">69.84%</td>
	<td data-value="63" class="abs medium">44/63</td>
	<td data-value="43.59" class="pct low">43.59%</td>
	<td data-value="78" class="abs low">34/78</td>
	<td data-value="70.59" class="pct medium">70.59%</td>
	<td data-value="17" class="abs medium">12/17</td>
	<td data-value="74.12" class="pct medium">74.12%</td>
	<td data-value="85" class="abs medium">63/85</td>
	</tr>

<tr>
	<td class="file low" data-value="HAOXPriceOracleV2.sol"><a href="HAOXPriceOracleV2.sol.html">HAOXPriceOracleV2.sol</a></td>
	<td data-value="2.5" class="pic low"><div class="chart"><div class="cover-fill" style="width: 2%;"></div><div class="cover-empty" style="width:98%;"></div></div></td>
	<td data-value="2.5" class="pct low">2.5%</td>
	<td data-value="40" class="abs low">1/40</td>
	<td data-value="1.85" class="pct low">1.85%</td>
	<td data-value="54" class="abs low">1/54</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="12" class="abs low">1/12</td>
	<td data-value="1.52" class="pct low">1.52%</td>
	<td data-value="66" class="abs low">1/66</td>
	</tr>

<tr>
	<td class="file low" data-value="HAOXTokenV2.sol"><a href="HAOXTokenV2.sol.html">HAOXTokenV2.sol</a></td>
	<td data-value="16.33" class="pic low"><div class="chart"><div class="cover-fill" style="width: 16%;"></div><div class="cover-empty" style="width:84%;"></div></div></td>
	<td data-value="16.33" class="pct low">16.33%</td>
	<td data-value="49" class="abs low">8/49</td>
	<td data-value="13.79" class="pct low">13.79%</td>
	<td data-value="58" class="abs low">8/58</td>
	<td data-value="41.67" class="pct low">41.67%</td>
	<td data-value="12" class="abs low">5/12</td>
	<td data-value="15.38" class="pct low">15.38%</td>
	<td data-value="65" class="abs low">10/65</td>
	</tr>

<tr>
	<td class="file medium" data-value="HAOXVestingV2Minimal.sol"><a href="HAOXVestingV2Minimal.sol.html">HAOXVestingV2Minimal.sol</a></td>
	<td data-value="79.63" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 79%;"></div><div class="cover-empty" style="width:21%;"></div></div></td>
	<td data-value="79.63" class="pct medium">79.63%</td>
	<td data-value="54" class="abs medium">43/54</td>
	<td data-value="59.68" class="pct medium">59.68%</td>
	<td data-value="62" class="abs medium">37/62</td>
	<td data-value="73.33" class="pct medium">73.33%</td>
	<td data-value="15" class="abs medium">11/15</td>
	<td data-value="79.49" class="pct medium">79.49%</td>
	<td data-value="78" class="abs medium">62/78</td>
	</tr>

<tr>
	<td class="file low" data-value="HAOXVestingV2Ultra.sol"><a href="HAOXVestingV2Ultra.sol.html">HAOXVestingV2Ultra.sol</a></td>
	<td data-value="0" class="pic low"><div class="chart"><div class="cover-fill" style="width: 0%;"></div><div class="cover-empty" style="width:100%;"></div></div></td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="48" class="abs low">0/48</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="50" class="abs low">0/50</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="61" class="abs low">0/61</td>
	</tr>

<tr>
	<td class="file medium" data-value="MockPriceOracle.sol"><a href="MockPriceOracle.sol.html">MockPriceOracle.sol</a></td>
	<td data-value="50" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 50%;"></div><div class="cover-empty" style="width:50%;"></div></div></td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="6" class="abs medium">3/6</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="10" class="abs medium">5/10</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 21:36:12 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
