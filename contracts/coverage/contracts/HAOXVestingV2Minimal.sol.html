<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXVestingV2Minimal.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXVestingV2Minimal.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/54</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/62</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/78</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
&nbsp;
/**
 * @title HAOXVestingV2Minimal
 * @dev 精简版HAOX代币解锁合约 - 成本优化版本
 * 保留核心安全功能，移除非必要特性以降低部署成本
 */
contract HAOXVestingV2Minimal is Ownable, ReentrancyGuard, Pausable {
    
    // 基础常量
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;
    uint256 public constant EMERGENCY_DELAY = 7 days;
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18;
    
    // 核心状态变量
    IERC20 public immutable haoxToken;
    address public immutable priceOracle;
    address public immutable projectWallet;
    address public immutable communityWallet;
    
    uint256 public currentRound = 1;
    
    // 轮次信息结构（优化存储）
    struct Round {
        uint128 triggerPrice;      // 触发价格 (8位小数)
        uint64 priceReachedTime;   // 价格达到时间
        uint64 unlockTime;         // 解锁时间
        bool priceConditionMet;    // 价格条件是否满足
        bool unlocked;             // 是否已解锁
    }
    
    // 紧急提取请求结构（精简版）
    struct EmergencyRequest {
        uint128 amount;
        uint64 requestTime;
        bool executed;
        address requester;
    }
    
    // 状态映射
    mapping(uint256 =&gt; Round) public rounds;
    mapping(bytes32 =&gt; EmergencyRequest) public emergencyRequests;
    mapping(address =&gt; bool) public emergencySigners;
    
    // 精简的价格历史（仅保留最近10条）
    struct PriceCheck {
        uint64 timestamp;
        uint64 price;
        bool conditionMet;
    }
    mapping(uint256 =&gt; PriceCheck[10]) public priceHistory;
    mapping(uint256 =&gt; uint8) public historyIndex;
    
    uint256 public requiredSignatures = 1;
    
    // 事件定义（精简版）
    event PriceConditionMet(uint256 indexed roundNumber, uint256 price, uint256 timestamp);
    event RoundUnlocked(uint256 indexed roundNumber, uint256 triggerPrice, uint256 projectTokens, uint256 communityTokens, uint256 timestamp);
    event PriceConditionReset(uint256 indexed roundNumber, uint256 price, uint256 timestamp);
    event EmergencyWithdrawRequested(bytes32 indexed requestId, uint256 amount, uint256 requestTime);
    event EmergencyWithdrawExecuted(bytes32 indexed requestId, uint256 amount);
&nbsp;
    /**
     * @dev 构造函数
     */
<span class="fstat-no" title="function not covered" >    constructor(</span>
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
<span class="cstat-no" title="statement not covered" >        require(_haoxToken != address(0), "Invalid token address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_priceOracle != address(0), "Invalid oracle address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_projectWallet != address(0), "Invalid project wallet")</span>;
<span class="cstat-no" title="statement not covered" >        require(_communityWallet != address(0), "Invalid community wallet")</span>;
        
        haoxToken = IERC20(_haoxToken);
        priceOracle = _priceOracle;
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        // 初始化紧急签名者
        emergencySigners[msg.sender] = true;
        
        // 初始化轮次数据（精简版）
<span class="cstat-no" title="statement not covered" >        _initializeRounds()</span>;
    }
&nbsp;
    /**
     * @dev 初始化轮次数据（Gas优化版本）
     */
<span class="fstat-no" title="function not covered" >    function _initializeRounds() internal {</span>
        // 使用紧凑的初始化方式
<span class="cstat-no" title="statement not covered" >        uint128[31] memory prices = [</span>
            uint128(0.01 * 10**8), uint128(0.015 * 10**8), uint128(0.02 * 10**8), uint128(0.025 * 10**8), uint128(0.03 * 10**8),
            uint128(0.035 * 10**8), uint128(0.04 * 10**8), uint128(0.045 * 10**8), uint128(0.05 * 10**8), uint128(0.055 * 10**8),
            uint128(0.06 * 10**8), uint128(0.065 * 10**8), uint128(0.07 * 10**8), uint128(0.075 * 10**8), uint128(0.08 * 10**8),
            uint128(0.085 * 10**8), uint128(0.09 * 10**8), uint128(0.095 * 10**8), uint128(0.1 * 10**8), uint128(0.11 * 10**8),
            uint128(0.12 * 10**8), uint128(0.13 * 10**8), uint128(0.14 * 10**8), uint128(0.15 * 10**8), uint128(0.16 * 10**8),
            uint128(0.17 * 10**8), uint128(0.18 * 10**8), uint128(0.19 * 10**8), uint128(0.2 * 10**8), uint128(0.22 * 10**8),
            uint128(0.25 * 10**8)
        ];
        
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; 31; i++) {</span>
            rounds[i + 1].triggerPrice = prices[i];
        }
    }
&nbsp;
    /**
     * @dev 检查价格条件（Gas优化版本）
     */
<span class="fstat-no" title="function not covered" >    function checkPriceCondition() external nonReentrant whenNotPaused {</span>
<span class="cstat-no" title="statement not covered" >        uint256 roundNumber = currentRound;</span>
        
<span class="cstat-no" title="statement not covered" >        if (roundNumber &gt; TOTAL_ROUNDS) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        
        // 获取当前价格
<span class="cstat-no" title="statement not covered" >        uint256 currentPrice = _getCurrentPrice();</span>
<span class="cstat-no" title="statement not covered" >        Round storage round = rounds[roundNumber];</span>
<span class="cstat-no" title="statement not covered" >        bool conditionMet = currentPrice &gt;= round.triggerPrice;</span>
        
        // 添加到历史记录（循环覆盖）
<span class="cstat-no" title="statement not covered" >        uint8 index = historyIndex[roundNumber];</span>
        priceHistory[roundNumber][index] = PriceCheck({
            timestamp: uint64(block.timestamp),
            price: uint64(currentPrice),
            conditionMet: conditionMet
        });
        historyIndex[roundNumber] = (index + 1) % 10;
        
<span class="cstat-no" title="statement not covered" >        if (conditionMet) {</span>
<span class="cstat-no" title="statement not covered" >            if (!round.priceConditionMet) {</span>
                round.priceConditionMet = true;
                round.priceReachedTime = uint64(block.timestamp);
<span class="cstat-no" title="statement not covered" >                emit PriceConditionMet(roundNumber, currentPrice, block.timestamp);</span>
            } else {
<span class="cstat-no" title="statement not covered" >                uint256 maintainedDuration = block.timestamp - round.priceReachedTime;</span>
<span class="cstat-no" title="statement not covered" >                if (maintainedDuration &gt;= PRICE_MAINTAIN_DURATION &amp;&amp; !round.unlocked) {</span>
<span class="cstat-no" title="statement not covered" >                    _unlockRound(roundNumber, currentPrice)</span>;
                }
            }
        } else {
<span class="cstat-no" title="statement not covered" >            if (round.priceConditionMet &amp;&amp; !round.unlocked) {</span>
                round.priceConditionMet = false;
                round.priceReachedTime = 0;
<span class="cstat-no" title="statement not covered" >                emit PriceConditionReset(roundNumber, currentPrice, block.timestamp);</span>
            }
        }
    }
&nbsp;
    /**
     * @dev 解锁轮次（精简版）
     */
<span class="fstat-no" title="function not covered" >    function _unlockRound(uint256 roundNumber, uint256 triggerPrice) internal {</span>
<span class="cstat-no" title="statement not covered" >        Round storage round = rounds[roundNumber];</span>
        round.unlocked = true;
        round.unlockTime = uint64(block.timestamp);
        
        // 计算代币分配（简化计算）
<span class="cstat-no" title="statement not covered" >        uint256 projectTokens = 80000000 * 10**18;</span> // 8000万项目代币
<span class="cstat-no" title="statement not covered" >        uint256 communityTokens = 80000000 * 10**18;</span> // 8000万社区代币
        
        // 转移代币
<span class="cstat-no" title="statement not covered" >        require(haoxToken.transfer(projectWallet, projectTokens), "Project transfer failed")</span>;
<span class="cstat-no" title="statement not covered" >        require(haoxToken.transfer(communityWallet, communityTokens), "Community transfer failed")</span>;
        
<span class="cstat-no" title="statement not covered" >        emit RoundUnlocked(roundNumber, triggerPrice, projectTokens, communityTokens, block.timestamp);</span>
        
        // 移动到下一轮
        currentRound = roundNumber + 1;
    }
&nbsp;
    /**
     * @dev 获取当前价格（精简版）
     */
<span class="fstat-no" title="function not covered" >    function _getCurrentPrice() internal view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        (bool success, bytes memory data) = priceOracle.staticcall(</span>
            abi.encodeWithSignature("getLatestPrice()")
        );
<span class="cstat-no" title="statement not covered" >        require(success &amp;&amp; data.length &gt;= 32, "Price oracle failed")</span>;
<span class="cstat-no" title="statement not covered" >        return abi.decode(data, (uint256));</span>
    }
&nbsp;
    /**
     * @dev 请求紧急提取（精简版）
     */
<span class="fstat-no" title="function not covered" >    function requestEmergencyWithdraw(uint256 amount) external whenPaused {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencySigners[msg.sender], "Not authorized")</span>;
<span class="cstat-no" title="statement not covered" >        require(amount &lt;= MAX_EMERGENCY_AMOUNT, "Amount too large")</span>;
        
<span class="cstat-no" title="statement not covered" >        bytes32 requestId = keccak256(abi.encodePacked(amount, block.timestamp, msg.sender));</span>
        
        emergencyRequests[requestId] = EmergencyRequest({
            amount: uint128(amount),
            requestTime: uint64(block.timestamp),
            executed: false,
            requester: msg.sender
        });
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyWithdrawRequested(requestId, amount, block.timestamp);</span>
    }
&nbsp;
    /**
     * @dev 执行紧急提取（精简版）
     */
<span class="fstat-no" title="function not covered" >    function executeEmergencyWithdraw(bytes32 requestId) external {</span>
<span class="cstat-no" title="statement not covered" >        EmergencyRequest storage request = emergencyRequests[requestId];</span>
<span class="cstat-no" title="statement not covered" >        require(request.requestTime &gt; 0, "Request not found")</span>;
<span class="cstat-no" title="statement not covered" >        require(!request.executed, "Already executed")</span>;
<span class="cstat-no" title="statement not covered" >        require(block.timestamp &gt;= request.requestTime + EMERGENCY_DELAY, "Time lock active")</span>;
<span class="cstat-no" title="statement not covered" >        require(emergencySigners[msg.sender], "Not authorized")</span>;
        
        request.executed = true;
<span class="cstat-no" title="statement not covered" >        require(haoxToken.transfer(owner(), request.amount), "Transfer failed")</span>;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyWithdrawExecuted(requestId, request.amount);</span>
    }
&nbsp;
    /**
     * @dev 获取解锁进度（精简版）
     */
<span class="fstat-no" title="function not covered" >    function getUnlockProgress() external view returns (</span>
        uint256 currentPrice,
        uint256 nextRound,
        uint256 targetPrice,
        bool conditionMet,
        uint256 timeRemaining
    ) {
        currentPrice = _getCurrentPrice();
        nextRound = currentRound;
        
<span class="cstat-no" title="statement not covered" >        if (nextRound &lt;= TOTAL_ROUNDS) {</span>
<span class="cstat-no" title="statement not covered" >            Round memory round = rounds[nextRound];</span>
            targetPrice = round.triggerPrice;
            conditionMet = round.priceConditionMet;
            
<span class="cstat-no" title="statement not covered" >            if (conditionMet &amp;&amp; round.priceReachedTime &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                uint256 elapsed = block.timestamp - round.priceReachedTime;</span>
                timeRemaining = elapsed &gt;= PRICE_MAINTAIN_DURATION ? 0 : PRICE_MAINTAIN_DURATION - elapsed;
            }
        }
    }
&nbsp;
    /**
     * @dev 获取轮次信息
     */
<span class="fstat-no" title="function not covered" >    function getRoundInfo(uint256 roundNumber) external view returns (</span>
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime,
        uint256 unlockTime
    ) {
<span class="cstat-no" title="statement not covered" >        require(roundNumber &gt; 0 &amp;&amp; roundNumber &lt;= TOTAL_ROUNDS, "Invalid round")</span>;
<span class="cstat-no" title="statement not covered" >        Round memory round = rounds[roundNumber];</span>
        
<span class="cstat-no" title="statement not covered" >        return (</span>
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime,
            round.unlockTime
        );
    }
&nbsp;
    /**
     * @dev 获取价格历史（精简版）
     */
<span class="fstat-no" title="function not covered" >    function getPriceHistory(uint256 roundNumber) external view returns (PriceCheck[10] memory) {</span>
<span class="cstat-no" title="statement not covered" >        return priceHistory[roundNumber];</span>
    }
&nbsp;
    /**
     * @dev 管理员功能
     */
<span class="fstat-no" title="function not covered" >    function addEmergencySigner(address signer) external onlyOwner {</span>
        emergencySigners[signer] = true;
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function removeEmergencySigner(address signer) external onlyOwner {</span>
        emergencySigners[signer] = false;
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function pause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
&nbsp;
    /**
     * @dev 紧急暂停（无时间锁）
     */
<span class="fstat-no" title="function not covered" >    function emergencyPause() external {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencySigners[msg.sender], "Not authorized")</span>;
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 13:08:54 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
