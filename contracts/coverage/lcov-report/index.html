<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">53.72% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>195/363</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">35.71% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>150/420</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">59.41% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>60/101</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">54.48% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>286/525</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="contracts/"><a href="contracts/index.html">contracts/</a></td>
	<td data-value="53.72" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 53%;"></div><div class="cover-empty" style="width:47%;"></div></div></td>
	<td data-value="53.72" class="pct medium">53.72%</td>
	<td data-value="363" class="abs medium">195/363</td>
	<td data-value="35.71" class="pct low">35.71%</td>
	<td data-value="420" class="abs low">150/420</td>
	<td data-value="59.41" class="pct medium">59.41%</td>
	<td data-value="101" class="abs medium">60/101</td>
	<td data-value="54.48" class="pct medium">54.48%</td>
	<td data-value="525" class="abs medium">286/525</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 23:52:50 GMT+0800 (中国标准时间)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
