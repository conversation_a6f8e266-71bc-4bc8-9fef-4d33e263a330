<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXPriceAggregatorMinimal.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXPriceAggregatorMinimal.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/63</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/78</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/17</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/85</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
&nbsp;
/**
 * @title HAOXPriceAggregatorMinimal
 * @dev 精简版价格聚合器 - 成本优化版本
 * 保留核心价格聚合功能，移除复杂特性以降低部署成本
 */
contract HAOXPriceAggregatorMinimal is Ownable, Pausable {
    
    // 精简的价格源结构
    struct PriceSource {
        address oracle;
        uint8 weight;
        bool active;
        uint32 lastUpdate;
    }
    
    // 聚合配置常量
    uint256 public constant MAX_PRICE_DEVIATION = 500; // 5%
    uint256 public constant PRICE_STALENESS_THRESHOLD = 3600; // 1小时
    uint256 public constant MIN_SOURCES_REQUIRED = 2;
    uint256 public constant MAX_SOURCES = 5; // 减少到5个源
    
    // 状态变量
    PriceSource[MAX_SOURCES] public priceSources;
    uint8 public sourceCount;
    
    // 最新价格数据
    uint256 public latestPrice;
    uint256 public lastUpdateTime;
    uint8 public lastSourceCount;
    
    // 紧急模式
    uint256 public emergencyPrice;
    uint256 public emergencyPriceTimestamp;
    bool public emergencyMode;
    
    // 事件定义（精简版）
    event PriceAggregated(uint256 price, uint256 timestamp, uint8 sourceCount);
    event PriceSourceAdded(uint8 indexed sourceId, address indexed oracle, uint8 weight);
    event PriceSourceUpdated(uint8 indexed sourceId, bool active, uint8 weight);
    event EmergencyModeActivated(uint256 price, uint256 timestamp);
    event EmergencyModeDeactivated();
&nbsp;
    /**
     * @dev 构造函数
     */
<span class="fstat-no" title="function not covered" >    constructor() Ownable(msg.sender) {</span>
        emergencyMode = false;
    }
&nbsp;
    /**
     * @dev 添加价格源（精简版）
     */
<span class="fstat-no" title="function not covered" >    function addPriceSource(address oracle, uint8 weight) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(oracle != address(0), "Invalid oracle")</span>;
<span class="cstat-no" title="statement not covered" >        require(weight &gt; 0 &amp;&amp; weight &lt;= 100, "Invalid weight")</span>;
<span class="cstat-no" title="statement not covered" >        require(sourceCount &lt; MAX_SOURCES, "Too many sources")</span>;
        
        // 检查重复
<span class="cstat-no" title="statement not covered" >        for (uint8 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            require(priceSources[i].oracle != oracle, "Oracle exists")</span>;
        }
        
        priceSources[sourceCount] = PriceSource({
            oracle: oracle,
            weight: weight,
            active: true,
            lastUpdate: 0
        });
        
<span class="cstat-no" title="statement not covered" >        emit PriceSourceAdded(sourceCount, oracle, weight);</span>
        sourceCount++;
    }
&nbsp;
    /**
     * @dev 更新价格源状态
     */
<span class="fstat-no" title="function not covered" >    function updatePriceSource(uint8 sourceId, bool active, uint8 weight) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(sourceId &lt; sourceCount, "Invalid source")</span>;
<span class="cstat-no" title="statement not covered" >        require(weight &gt; 0 &amp;&amp; weight &lt;= 100, "Invalid weight")</span>;
        
        priceSources[sourceId].active = active;
        priceSources[sourceId].weight = weight;
        
<span class="cstat-no" title="statement not covered" >        emit PriceSourceUpdated(sourceId, active, weight);</span>
    }
&nbsp;
    /**
     * @dev 获取最新价格
     */
<span class="fstat-no" title="function not covered" >    function getLatestPrice() external view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        if (emergencyMode) {</span>
<span class="cstat-no" title="statement not covered" >            require(</span>
                block.timestamp - emergencyPriceTimestamp &lt;= PRICE_STALENESS_THRESHOLD * 2,
                "Emergency price stale"
            );
<span class="cstat-no" title="statement not covered" >            return emergencyPrice;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        require(lastUpdateTime &gt; 0, "No price available")</span>;
<span class="cstat-no" title="statement not covered" >        require(</span>
            block.timestamp - lastUpdateTime &lt;= PRICE_STALENESS_THRESHOLD,
            "Price stale"
        );
        
<span class="cstat-no" title="statement not covered" >        return latestPrice;</span>
    }
&nbsp;
    /**
     * @dev 获取最后更新时间
     */
<span class="fstat-no" title="function not covered" >    function getLastUpdateTime() external view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        return emergencyMode ? emergencyPriceTimestamp : lastUpdateTime;</span>
    }
&nbsp;
    /**
     * @dev 更新聚合价格（精简版）
     */
<span class="fstat-no" title="function not covered" >    function updateAggregatedPrice() external whenNotPaused {</span>
<span class="cstat-no" title="statement not covered" >        require(!emergencyMode, "Emergency mode active")</span>;
        
<span class="cstat-no" title="statement not covered" >        uint256 totalWeight = 0;</span>
<span class="cstat-no" title="statement not covered" >        uint256 weightedSum = 0;</span>
<span class="cstat-no" title="statement not covered" >        uint8 validSources = 0;</span>
        
        // 收集有效价格
<span class="cstat-no" title="statement not covered" >        for (uint8 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (!priceSources[i].active) continue;</span>
&nbsp;
            // 使用低级调用替代try-catch
<span class="cstat-no" title="statement not covered" >            (bool success, uint256 price) = _tryGetPriceFromSource(i);</span>
<span class="cstat-no" title="statement not covered" >            if (!success) continue;</span>
&nbsp;
            // 基本价格验证
<span class="cstat-no" title="statement not covered" >            if (price == 0 || price &gt; 1e18) continue;</span>
&nbsp;
            weightedSum += price * priceSources[i].weight;
            totalWeight += priceSources[i].weight;
            validSources++;
&nbsp;
            priceSources[i].lastUpdate = uint32(block.timestamp);
        }
        
<span class="cstat-no" title="statement not covered" >        require(validSources &gt;= MIN_SOURCES_REQUIRED, "Insufficient sources")</span>;
        
<span class="cstat-no" title="statement not covered" >        uint256 aggregatedPrice = weightedSum / totalWeight;</span>
        
        // 简化的偏差检测
<span class="cstat-no" title="statement not covered" >        _validatePriceDeviation(aggregatedPrice)</span>;
        
        latestPrice = aggregatedPrice;
        lastUpdateTime = block.timestamp;
        lastSourceCount = validSources;
        
<span class="cstat-no" title="statement not covered" >        emit PriceAggregated(aggregatedPrice, block.timestamp, validSources);</span>
    }
&nbsp;
    /**
     * @dev 从价格源获取价格
     */
<span class="fstat-no" title="function not covered" >    function _getPriceFromSource(uint8 sourceId) internal view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        address oracle = priceSources[sourceId].oracle;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        (bool success, bytes memory data) = oracle.staticcall(</span>
            abi.encodeWithSignature("getLatestPrice()")
        );
&nbsp;
<span class="cstat-no" title="statement not covered" >        require(success &amp;&amp; data.length &gt;= 32, "Oracle call failed")</span>;
<span class="cstat-no" title="statement not covered" >        return abi.decode(data, (uint256));</span>
    }
&nbsp;
    /**
     * @dev 尝试从价格源获取价格（安全版本）
     */
<span class="fstat-no" title="function not covered" >    function _tryGetPriceFromSource(uint8 sourceId) internal view returns (bool success, uint256 price) {</span>
<span class="cstat-no" title="statement not covered" >        address oracle = priceSources[sourceId].oracle;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        (bool callSuccess, bytes memory data) = oracle.staticcall(</span>
            abi.encodeWithSignature("getLatestPrice()")
        );
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (callSuccess &amp;&amp; data.length &gt;= 32) {</span>
            price = abi.decode(data, (uint256));
            success = true;
        } else {
            success = false;
            price = 0;
        }
    }
&nbsp;
    /**
     * @dev 验证价格偏差（简化版）
     */
<span class="fstat-no" title="function not covered" >    function _validatePriceDeviation(uint256 newPrice) internal view {</span>
<span class="cstat-no" title="statement not covered" >        if (latestPrice == 0) <span class="cstat-no" title="statement not covered" >return;</span></span>
        
<span class="cstat-no" title="statement not covered" >        uint256 deviation = newPrice &gt; latestPrice</span>
            ? (newPrice - latestPrice) * 10000 / latestPrice
            : (latestPrice - newPrice) * 10000 / latestPrice;
            
<span class="cstat-no" title="statement not covered" >        require(deviation &lt;= MAX_PRICE_DEVIATION * 3, "Price deviation too high")</span>;
    }
&nbsp;
    /**
     * @dev 激活紧急模式
     */
<span class="fstat-no" title="function not covered" >    function activateEmergencyMode(uint256 price) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(price &gt; 0, "Invalid price")</span>;
        
        emergencyMode = true;
        emergencyPrice = price;
        emergencyPriceTimestamp = block.timestamp;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyModeActivated(price, block.timestamp);</span>
    }
&nbsp;
    /**
     * @dev 停用紧急模式
     */
<span class="fstat-no" title="function not covered" >    function deactivateEmergencyMode() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencyMode, "Not in emergency mode")</span>;
        
<span class="cstat-no" title="statement not covered" >        uint8 activeCount = 0;</span>
<span class="cstat-no" title="statement not covered" >        for (uint8 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (priceSources[i].active) activeCount++;</span>
        }
<span class="cstat-no" title="statement not covered" >        require(activeCount &gt;= MIN_SOURCES_REQUIRED, "Insufficient sources")</span>;
        
        emergencyMode = false;
        emergencyPrice = 0;
        emergencyPriceTimestamp = 0;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyModeDeactivated();</span>
    }
&nbsp;
    /**
     * @dev 获取聚合器状态
     */
<span class="fstat-no" title="function not covered" >    function getAggregatorStatus() external view returns (</span>
        uint8 totalSources,
        uint8 activeSources,
        bool isEmergencyMode,
        uint256 lastUpdate,
        uint256 currentPrice
    ) {
<span class="cstat-no" title="statement not covered" >        uint8 active = 0;</span>
<span class="cstat-no" title="statement not covered" >        for (uint8 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (priceSources[i].active) active++;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        return (</span>
            sourceCount,
            active,
            emergencyMode,
            lastUpdateTime,
            latestPrice
        );
    }
&nbsp;
    /**
     * @dev 获取价格源信息
     */
<span class="fstat-no" title="function not covered" >    function getPriceSource(uint8 sourceId) external view returns (</span>
        address oracle,
        uint8 weight,
        bool active,
        uint32 lastUpdate
    ) {
<span class="cstat-no" title="statement not covered" >        require(sourceId &lt; sourceCount, "Invalid source")</span>;
<span class="cstat-no" title="statement not covered" >        PriceSource memory source = priceSources[sourceId];</span>
        
<span class="cstat-no" title="statement not covered" >        return (source.oracle, source.weight, source.active, source.lastUpdate);</span>
    }
&nbsp;
    /**
     * @dev 批量更新价格源状态
     */
<span class="fstat-no" title="function not covered" >    function batchUpdateSources(uint8[] calldata sourceIds, bool[] calldata activeStates) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(sourceIds.length == activeStates.length, "Length mismatch")</span>;
        
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; sourceIds.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (sourceIds[i] &lt; sourceCount) {</span>
                priceSources[sourceIds[i]].active = activeStates[i];
<span class="cstat-no" title="statement not covered" >                emit PriceSourceUpdated(sourceIds[i], activeStates[i], priceSources[sourceIds[i]].weight);</span>
            }
        }
    }
&nbsp;
    /**
     * @dev 暂停/恢复合约
     */
<span class="fstat-no" title="function not covered" >    function pause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
&nbsp;
    /**
     * @dev 紧急暂停
     */
<span class="fstat-no" title="function not covered" >    function emergencyPause() external {</span>
        // 简化权限检查
<span class="cstat-no" title="statement not covered" >        require(msg.sender == owner(), "Not authorized")</span>;
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 12:47:30 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
