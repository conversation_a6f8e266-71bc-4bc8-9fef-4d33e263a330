<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">2.51% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>9/359</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">2.14% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>9/420</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">6.32% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>6/95</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">2.14% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>11/515</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="contracts/"><a href="contracts/index.html">contracts/</a></td>
	<td data-value="2.51" class="pic low"><div class="chart"><div class="cover-fill" style="width: 2%;"></div><div class="cover-empty" style="width:98%;"></div></div></td>
	<td data-value="2.51" class="pct low">2.51%</td>
	<td data-value="359" class="abs low">9/359</td>
	<td data-value="2.14" class="pct low">2.14%</td>
	<td data-value="420" class="abs low">9/420</td>
	<td data-value="6.32" class="pct low">6.32%</td>
	<td data-value="95" class="abs low">6/95</td>
	<td data-value="2.14" class="pct low">2.14%</td>
	<td data-value="515" class="abs low">11/515</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 13:08:54 GMT+0800 (中国标准时间)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
