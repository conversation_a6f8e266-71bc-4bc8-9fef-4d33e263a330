<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">37.74% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>137/363</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">25.24% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>106/420</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">41.58% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>42/101</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">38.67% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>203/525</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="contracts/"><a href="contracts/index.html">contracts/</a></td>
	<td data-value="37.74" class="pic low"><div class="chart"><div class="cover-fill" style="width: 37%;"></div><div class="cover-empty" style="width:63%;"></div></div></td>
	<td data-value="37.74" class="pct low">37.74%</td>
	<td data-value="363" class="abs low">137/363</td>
	<td data-value="25.24" class="pct low">25.24%</td>
	<td data-value="420" class="abs low">106/420</td>
	<td data-value="41.58" class="pct low">41.58%</td>
	<td data-value="101" class="abs low">42/101</td>
	<td data-value="38.67" class="pct low">38.67%</td>
	<td data-value="525" class="abs low">203/525</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 21:36:12 GMT+0800 (中国标准时间)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
