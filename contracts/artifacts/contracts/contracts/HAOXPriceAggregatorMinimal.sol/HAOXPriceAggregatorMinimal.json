{"_format": "hh-sol-artifact-1", "contractName": "HAOXPriceAggregatorMinimal", "sourceName": "contracts/contracts/HAOXPriceAggregatorMinimal.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EmergencyModeDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "sourceCount", "type": "uint8"}], "name": "PriceAggregated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint8", "name": "sourceId", "type": "uint8"}, {"indexed": true, "internalType": "address", "name": "oracle", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "PriceSourceAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint8", "name": "sourceId", "type": "uint8"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}, {"indexed": false, "internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "PriceSourceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "MAX_PRICE_DEVIATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SOURCES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_SOURCES_REQUIRED", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_STALENESS_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "addPriceSource", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8[]", "name": "sourceIds", "type": "uint8[]"}, {"internalType": "bool[]", "name": "activeStates", "type": "bool[]"}], "name": "batchUpdateSources", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPriceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAggregatorStatus", "outputs": [{"internalType": "uint8", "name": "totalSources", "type": "uint8"}, {"internalType": "uint8", "name": "activeSources", "type": "uint8"}, {"internalType": "bool", "name": "isEmergencyMode", "type": "bool"}, {"internalType": "uint256", "name": "lastUpdate", "type": "uint256"}, {"internalType": "uint256", "name": "currentPrice", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "sourceId", "type": "uint8"}], "name": "getPriceSource", "outputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint32", "name": "lastUpdate", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastSourceCount", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceSources", "outputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint32", "name": "lastUpdate", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "sourceCount", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "updateAggregatedPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "sourceId", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "updatePriceSource", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}