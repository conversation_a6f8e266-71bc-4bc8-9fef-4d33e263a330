{"_format": "hh-sol-artifact-1", "contractName": "HAOXPriceOracleV2", "sourceName": "contracts/contracts/HAOXPriceOracleV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_bnbUsdChainlink", "type": "address"}, {"internalType": "address", "name": "_haoxBnbPair", "type": "address"}, {"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_wbnb", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "fixedPrice", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EmergencyModeDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "sourceType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "PriceSourceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "haoxPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "bnbPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "confidence", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "source", "type": "string"}], "name": "PriceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "MAX_PRICE_DEVIATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_CONFIDENCE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_STALENESS_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPDATE_INTERVAL", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_fixedPrice", "type": "uint256"}], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "bnbUsdChainlink", "outputs": [{"internalType": "contract AggregatorV3Interface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentBnbPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentHaoxPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getBnbUsdPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getHaoxBnbPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getHaoxUsdPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPriceWithConfidence", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxBnbPair", "outputs": [{"internalType": "contract IPancakeSwapPair", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceHistory", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "updatePrices", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "wbnb", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}