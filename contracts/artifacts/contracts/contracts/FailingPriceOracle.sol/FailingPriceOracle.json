{"_format": "hh-sol-artifact-1", "contractName": "FailingPriceOracle", "sourceName": "contracts/contracts/FailingPriceOracle.sol", "abi": [{"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}], "bytecode": "0x6080604052348015600f57600080fd5b5060ba8061001e6000396000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c80638e15f47314602d575b600080fd5b60336045565b60405190815260200160405180910390f35b60405162461bcd60e51b815260206004820152600d60248201526c13dc9858db194819985a5b1959609a1b604482015260009060640160405180910390fdfea26469706673582212204b58bc987a243415a619ab10b6a09c1ef8c141b46e9ec26f7defb80a2235ca6c64736f6c63430008140033", "deployedBytecode": "0x6080604052348015600f57600080fd5b506004361060285760003560e01c80638e15f47314602d575b600080fd5b60336045565b60405190815260200160405180910390f35b60405162461bcd60e51b815260206004820152600d60248201526c13dc9858db194819985a5b1959609a1b604482015260009060640160405180910390fdfea26469706673582212204b58bc987a243415a619ab10b6a09c1ef8c141b46e9ec26f7defb80a2235ca6c64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}