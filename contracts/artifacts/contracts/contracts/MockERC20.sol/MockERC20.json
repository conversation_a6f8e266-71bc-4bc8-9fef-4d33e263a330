{"_format": "hh-sol-artifact-1", "contractName": "MockERC20", "sourceName": "contracts/contracts/MockERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}