{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2Ultra", "sourceName": "contracts/contracts/HAOXVestingV2Ultra.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "EMERGENCY_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTAIN_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "communityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "emergencyRequests", "outputs": [{"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint64", "name": "requestTime", "type": "uint64"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencySigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "address", "name": "token", "type": "address"}], "name": "executeEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getCurrentPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "totalRounds", "type": "uint256"}, {"internalType": "uint256", "name": "currentRoundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "unlockedRounds", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "projectWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "requestEmergencyWithdraw", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rounds", "outputs": [{"internalType": "uint128", "name": "triggerPrice", "type": "uint128"}, {"internalType": "uint64", "name": "priceReachedTime", "type": "uint64"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bool", "name": "status", "type": "bool"}], "name": "setEmergency<PERSON><PERSON>er", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_required", "type": "uint256"}], "name": "setRequiredSignatures", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}