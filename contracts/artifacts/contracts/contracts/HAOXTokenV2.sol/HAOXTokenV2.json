{"_format": "hh-sol-artifact-1", "contractName": "HAOXTokenV2", "sourceName": "contracts/contracts/HAOXTokenV2.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "contractType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "ContractAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "EmergencyPauseActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "lockId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "unlockTime", "type": "uint256"}], "name": "TimeLockCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "lockId", "type": "bytes32"}], "name": "TimeLockExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "INITIAL_UNLOCK", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LOCKED_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_PAUSE_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRESALE_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PROJECT_RESERVE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TIMELOCK_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "autoUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getContractStatus", "outputs": [{"internalType": "bool", "name": "isPaused", "type": "bool"}, {"internalType": "uint256", "name": "pauseDuration", "type": "uint256"}, {"internalType": "uint256", "name": "remainingPauseTime", "type": "uint256"}, {"internalType": "address", "name": "presale", "type": "address"}, {"internalType": "address", "name": "invitation", "type": "address"}, {"internalType": "address", "name": "vesting", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "invitationContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pauseStartTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "presaleContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_invitationContract", "type": "address"}], "name": "setInvitationContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_presaleContract", "type": "address"}], "name": "setPresaleContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_priceOracle", "type": "address"}], "name": "setPriceO<PERSON>le", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingContract", "type": "address"}], "name": "setVestingContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "timeLocks", "outputs": [{"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "vestingContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}