{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2Minimal", "sourceName": "contracts/contracts/HAOXVestingV2Minimal.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AlreadyExecuted", "type": "error"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidRound", "type": "error"}, {"inputs": [], "name": "NotAuthorized", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "PriceOracleFailed", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "RequestNotFound", "type": "error"}, {"inputs": [], "name": "TimeLockActive", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "requestTime", "type": "uint256"}], "name": "EmergencyWithdrawRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionReset", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "communityTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "COMMUNITY_WALLET", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EMERGENCY_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "HAOX_TOKEN", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTAIN_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_ORACLE", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PROJECT_WALLET", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "addEmergencySigner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "emergencyRequests", "outputs": [{"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint64", "name": "requestTime", "type": "uint64"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencySigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "executeEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getPriceHistory", "outputs": [{"components": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint64", "name": "price", "type": "uint64"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "internalType": "struct HAOXVestingV2Minimal.PriceCheck[10]", "name": "", "type": "tuple[10]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "currentPrice", "type": "uint256"}, {"internalType": "uint256", "name": "nextRound", "type": "uint256"}, {"internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}, {"internalType": "uint256", "name": "timeRemaining", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "historyIndex", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceHistory", "outputs": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint64", "name": "price", "type": "uint64"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "requestEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rounds", "outputs": [{"internalType": "uint128", "name": "triggerPrice", "type": "uint128"}, {"internalType": "uint64", "name": "priceReachedTime", "type": "uint64"}, {"internalType": "uint64", "name": "unlockTime", "type": "uint64"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}