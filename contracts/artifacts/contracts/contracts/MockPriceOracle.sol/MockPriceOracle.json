{"_format": "hh-sol-artifact-1", "contractName": "MockPriceO<PERSON>le", "sourceName": "contracts/contracts/MockPriceOracle.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "fixedPrice", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPrice", "type": "uint256"}], "name": "PriceUpdated", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "fixedPrice", "type": "uint256"}], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPrice", "type": "uint256"}], "name": "setPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x608060405234801561001057600080fd5b50662386f26fc100006000556001805460ff1916905561018b806100356000396000f3fe608060405234801561001057600080fd5b50600436106100575760003560e01c806320a194b81461005c5780638e15f4731461007757806391b7f5ed1461008857806399b20eaf1461009d578063d9c5e6d7146100ac575b600080fd5b60015460ff1660405190151581526020015b60405180910390f35b60005460405190815260200161006e565b61009b61009636600461013c565b6100bf565b005b61009b6001805460ff19169055565b61009b6100ba36600461013c565b6100fb565b60008190556040518181527f66cbca4f3c64fecf1dcb9ce094abcf7f68c3450a1d4e3a8e917dd621edb4ebe0906020015b60405180910390a150565b6001805460ff19168117905560008190556040518181527f5fa9abb83376468762d4533d9cd0a8f21edf0033fa06b47347578fb235a92edc906020016100f0565b60006020828403121561014e57600080fd5b503591905056fea264697066735822122071126e205397769ab3661a6f049d79f08628e0e92a0335bd7e6a3bb20982ce5e64736f6c63430008140033", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106100575760003560e01c806320a194b81461005c5780638e15f4731461007757806391b7f5ed1461008857806399b20eaf1461009d578063d9c5e6d7146100ac575b600080fd5b60015460ff1660405190151581526020015b60405180910390f35b60005460405190815260200161006e565b61009b61009636600461013c565b6100bf565b005b61009b6001805460ff19169055565b61009b6100ba36600461013c565b6100fb565b60008190556040518181527f66cbca4f3c64fecf1dcb9ce094abcf7f68c3450a1d4e3a8e917dd621edb4ebe0906020015b60405180910390a150565b6001805460ff19168117905560008190556040518181527f5fa9abb83376468762d4533d9cd0a8f21edf0033fa06b47347578fb235a92edc906020016100f0565b60006020828403121561014e57600080fd5b503591905056fea264697066735822122071126e205397769ab3661a6f049d79f08628e0e92a0335bd7e6a3bb20982ce5e64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}