{"_format": "hh-sol-artifact-1", "contractName": "HAOXPresaleV2", "sourceName": "contracts/contracts/HAOXPresaleV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_invitationContract", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "totalBNBRaised", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokensSold", "type": "uint256"}], "name": "PresaleEnded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "stage", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokensSold", "type": "uint256"}], "name": "StageCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "bnbAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "avgRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "startStage", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "endStage", "type": "uint256"}], "name": "TokensPurchased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "INITIAL_RATE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_INVESTMENT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_INVESTMENT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RATE_DECREASE_PERCENT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TARGET_BNB", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKENS_PER_STAGE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_PRESALE_TOKENS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_STAGES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "users", "type": "address[]"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inviter", "type": "address"}], "name": "buyTokens", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "bnbAmount", "type": "uint256"}], "name": "calculateCrossStageTokens", "outputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"internalType": "uint256", "name": "avgRate", "type": "uint256"}, {"internalType": "uint256", "name": "endStage", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentStage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getCurrentRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPresaleStatus", "outputs": [{"internalType": "uint256", "name": "_currentStage", "type": "uint256"}, {"internalType": "uint256", "name": "_tokensRemainingInCurrentStage", "type": "uint256"}, {"internalType": "uint256", "name": "_totalBNBRaised", "type": "uint256"}, {"internalType": "uint256", "name": "_totalTokensSold", "type": "uint256"}, {"internalType": "uint256", "name": "_currentRate", "type": "uint256"}, {"internalType": "bool", "name": "_isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "stage", "type": "uint256"}], "name": "getStageRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract HAOXTokenV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initializeRates", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "investments", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "invitationContract", "outputs": [{"internalType": "contract HAOXInvitationV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "presaleActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "contract HAOXPriceOracleV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokensPurchased", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokensRemainingInCurrentStage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalBNBRaised", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalTokensSold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "whitelist", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawBNB", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x60a06040523480156200001157600080fd5b50604051620016a1380380620016a1833981016040819052620000349162000257565b600160005533806200006157604051631e4fbdf760e01b8152600060048201526024015b60405180910390fd5b6200006c81620001e0565b506001600160a01b038316620000c55760405162461bcd60e51b815260206004820152601560248201527f496e76616c696420746f6b656e20616464726573730000000000000000000000604482015260640162000058565b6001600160a01b0382166200011d5760405162461bcd60e51b815260206004820152601a60248201527f496e76616c696420696e7669746174696f6e2061646472657373000000000000604482015260640162000058565b6001600160a01b038116620001755760405162461bcd60e51b815260206004820152601660248201527f496e76616c6964206f7261636c65206164647265737300000000000000000000604482015260640162000058565b6001600160a01b03838116608052600280546001600160a01b031990811685841617909155600380549091169183169190911790556000600455620001c760646aa56fa5b99019a5c8000000620002a1565b60075550506008805460ff1916600117905550620002c4565b600180546001600160a01b03838116610100818102610100600160a81b031985161790945560405193909204169182907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b80516001600160a01b03811681146200025257600080fd5b919050565b6000806000606084860312156200026d57600080fd5b62000278846200023a565b925062000288602085016200023a565b915062000298604085016200023a565b90509250925092565b600082620002bf57634e487b7160e01b600052601260045260246000fd5b500490565b6080516113c1620002e0600039600061029401526113c16000f3fe6080604052600436106101ee5760003560e01c806363b201171161010d5780639b19251a116100a0578063dfeb023b1161006f578063dfeb023b1461058e578063ec8ac4d8146105ae578063f2fde38b146105c1578063f7fb07b0146105e1578063ffe1da8b146105f657600080fd5b80639b19251a146104f05780639eafe96214610520578063d7517caa14610535578063ddb619ab1461057957600080fd5b80638456cb59116100dc5780638456cb59146104755780638da5cb5b1461048a57806396b98862146104ad5780639993f6b2146104da57600080fd5b806363b201171461040b578063715018a614610421578063763fd2ab146104365780637f6497831461045557600080fd5b8063485bd3a21161018557806351b7e9bc1161015457806351b7e9bc1461039d57806353135ca0146103b35780635bf5d54c146103dd5780635c975abb146103f357600080fd5b8063485bd3a2146103305780634c76361e1461034d5780634dbac7331461036a5780634ef8ff331461038157600080fd5b806325dbec9d116101c157806325dbec9d146102825780632630c12f146102ce5780632ad92740146102ee5780633f4ba83a1461031b57600080fd5b80630e075dd1146101f35780631d111d131461021b5780631fec6c6a1461023257806321991d4614610247575b600080fd5b3480156101ff57600080fd5b50610208610616565b6040519081526020015b60405180910390f35b34801561022757600080fd5b5061023061062f565b005b34801561023e57600080fd5b50610208606281565b34801561025357600080fd5b50610267610262366004611223565b6106c9565b60408051938452602084019290925290820152606001610212565b34801561028e57600080fd5b506102b67f000000000000000000000000000000000000000000000000000000000000000081565b6040516001600160a01b039091168152602001610212565b3480156102da57600080fd5b506003546102b6906001600160a01b031681565b3480156102fa57600080fd5b5061020861030936600461123c565b600a6020526000908152604090205481565b34801561032757600080fd5b5061023061081e565b34801561033c57600080fd5b50610208681158e460913d00000081565b34801561035957600080fd5b506102086801158e460913d0000081565b34801561037657600080fd5b50610208621d2d3d81565b34801561038d57600080fd5b5061020867016345785d8a000081565b3480156103a957600080fd5b5061020860055481565b3480156103bf57600080fd5b506008546103cd9060ff1681565b6040519015158152602001610212565b3480156103e957600080fd5b5061020860045481565b3480156103ff57600080fd5b5060015460ff166103cd565b34801561041757600080fd5b5061020860065481565b34801561042d57600080fd5b50610230610830565b34801561044257600080fd5b506102086aa56fa5b99019a5c800000081565b34801561046157600080fd5b5061023061047036600461126c565b610842565b34801561048157600080fd5b506102306108c1565b34801561049657600080fd5b5060015461010090046001600160a01b03166102b6565b3480156104b957600080fd5b506102086104c836600461123c565b60096020526000908152604090205481565b3480156104e657600080fd5b5061020860075481565b3480156104fc57600080fd5b506103cd61050b36600461123c565b600b6020526000908152604090205460ff1681565b34801561052c57600080fd5b506102306108d1565b34801561054157600080fd5b5061054a6109b6565b6040805196875260208701959095529385019290925260608401526080830152151560a082015260c001610212565b34801561058557600080fd5b50610208606481565b34801561059a57600080fd5b506002546102b6906001600160a01b031681565b6102306105bc36600461123c565b6109f0565b3480156105cd57600080fd5b506102306105dc36600461123c565b610e97565b3480156105ed57600080fd5b50610208610ed2565b34801561060257600080fd5b50610208610611366004611223565b610ee4565b61062c60646aa56fa5b99019a5c80000006112f7565b81565b610637610f85565b60085460ff16156106865760405162461bcd60e51b815260206004820152601460248201527350726573616c65207374696c6c2061637469766560601b60448201526064015b60405180910390fd5b6001546040516001600160a01b0361010090920491909116904780156108fc02916000818181858888f193505050501580156106c6573d6000803e3d6000fd5b50565b6004546007546000918291829185918391905b6000841180156106ec5750606482105b156107a35760006106fc83610ee4565b90506000670de0b6b3a76400006107138388611319565b61071d91906112f7565b905082811161073b576107308186611336565b94506000955061079c565b60008261075085670de0b6b3a7640000611319565b61075a91906112f7565b90506107668487611336565b95506107728188611349565b96508461077e8161135c565b9550610798905060646aa56fa5b99019a5c80000006112f7565b9350505b50506106dc565b83156107f15760405162461bcd60e51b815260206004820152601d60248201527f496e73756666696369656e7420746f6b656e7320617661696c61626c65000000604482015260640161067d565b8761080484670de0b6b3a7640000611319565b61080e91906112f7565b9298929750909550909350505050565b610826610f85565b61082e610fb8565b565b610838610f85565b61082e600061100a565b61084a610f85565b60005b818110156108bc576001600b600085858581811061086d5761086d611375565b9050602002016020810190610882919061123c565b6001600160a01b031681526020810191909152604001600020805460ff1916911515919091179055806108b48161135c565b91505061084d565b505050565b6108c9610f85565b61082e611064565b6108d9610f85565b600d5460ff161561092c5760405162461bcd60e51b815260206004820152601960248201527f526174657320616c726561647920696e697469616c697a656400000000000000604482015260640161067d565b60008052600c602052621d2d3d7f13649b2456f1b42fef0f0040b3aaeabcd21a76a0f3f5defd4f583839455116e881905560015b60648110156109a5576064610976606284611319565b61098091906112f7565b6000828152600c6020526040902081905591508061099d8161135c565b915050610960565b5050600d805460ff19166001179055565b6000806000806000806004546007546005546006546109d6600454610ee4565b600854949b939a509198509650945060ff90911692509050565b6109f861109f565b610a006110c9565b60085460ff16610a475760405162461bcd60e51b815260206004820152601260248201527150726573616c65206e6f742061637469766560701b604482015260640161067d565b336000908152600b602052604090205460ff16610a985760405162461bcd60e51b815260206004820152600f60248201526e139bdd081dda1a5d195b1a5cdd1959608a1b604482015260640161067d565b67016345785d8a0000341015610af05760405162461bcd60e51b815260206004820152601860248201527f42656c6f77206d696e696d756d20696e766573746d656e740000000000000000604482015260640161067d565b336000908152600960205260409020546801158e460913d0000090610b16903490611336565b1115610b645760405162461bcd60e51b815260206004820152601a60248201527f45786365656473206d6178696d756d20696e766573746d656e74000000000000604482015260640161067d565b681158e460913d00000034600554610b7c9190611336565b1115610bbf5760405162461bcd60e51b815260206004820152601260248201527122bc31b2b2b239903a30b933b2ba1021272160711b604482015260640161067d565b60045460008080610bcf346106c9565b92509250925060008311610c1c5760405162461bcd60e51b8152602060048201526014602482015273125b9d985b1a59081d1bdad95b88185b5bdd5b9d60621b604482015260640161067d565b6aa56fa5b99019a5c800000083600654610c369190611336565b1115610c845760405162461bcd60e51b815260206004820152601c60248201527f4578636565647320746f74616c2070726573616c6520746f6b656e7300000000604482015260640161067d565b610c8f3484836110ed565b3360009081526009602052604081208054349290610cae908490611336565b9091555050336000908152600a602052604081208054859290610cd2908490611336565b90915550506001600160a01b03851615801590610cf857506001600160a01b0385163314155b15610d635760025460405163419794d760e11b81526001600160a01b0387811660048301523360248301523460448301529091169063832f29ae90606401600060405180830381600087803b158015610d5057600080fd5b505af1925050508015610d61575060015b505b6040805134815260208101859052908101839052606081018590526080810182905233907f6aca558a17acdb28cf36bd17a224740ed6c6a73de4aff1f9aca8c03ec27d2ea89060a00160405180910390a2681158e460913d000000600554101580610ddb57506aa56fa5b99019a5c800000060065410155b15610e89576008805460ff191690556002546040805163a43be57b60e01b815290516001600160a01b039092169163a43be57b9160048082019260009290919082900301818387803b158015610e3057600080fd5b505af1925050508015610e41575060015b507fb4e6cb40663c1ac63bcd620dbe3d98c44526baa45e310f545f425ebaab36a293600554600654604051610e80929190918252602082015260400190565b60405180910390a15b505050506106c66001600055565b610e9f610f85565b6001600160a01b038116610ec957604051631e4fbdf760e01b81526000600482015260240161067d565b6106c68161100a565b6000610edf600454610ee4565b905090565b600060648210610f265760405162461bcd60e51b815260206004820152600d60248201526c496e76616c696420737461676560981b604482015260640161067d565b600d5460ff1615610f4457506000908152600c602052604090205490565b621d2d3d60005b83811015610f7e576064610f60606284611319565b610f6a91906112f7565b915080610f768161135c565b915050610f4b565b5092915050565b6001546001600160a01b0361010090910416331461082e5760405163118cdaa760e01b815233600482015260240161067d565b610fc0611200565b6001805460ff191690557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa335b6040516001600160a01b03909116815260200160405180910390a1565b600180546001600160a01b03838116610100818102610100600160a81b031985161790945560405193909204169182907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b61106c6110c9565b6001805460ff1916811790557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a25833610fed565b6002600054036110c257604051633ee5aeb560e01b815260040160405180910390fd5b6002600055565b60015460ff161561082e5760405163d93c066560e01b815260040160405180910390fd5b82825b60008111801561110257506064600454105b156111c95760075481116111315780600760008282546111229190611349565b90915550600091506110f09050565b60075461113e9082611349565b90507f38ca9a0cef5bad2d3e13b8a99a6077611bcf4631f4ddb0ae733a751179169c1160045482866111709190611349565b60065461117d9190611336565b6040805192835260208301919091520160405180910390a1600480549060006111a58361135c565b909155506111c1905060646aa56fa5b99019a5c80000006112f7565b6007556110f0565b84600560008282546111db9190611336565b9250508190555083600660008282546111f49190611336565b90915550505050505050565b60015460ff1661082e57604051638dfc202b60e01b815260040160405180910390fd5b60006020828403121561123557600080fd5b5035919050565b60006020828403121561124e57600080fd5b81356001600160a01b038116811461126557600080fd5b9392505050565b6000806020838503121561127f57600080fd5b823567ffffffffffffffff8082111561129757600080fd5b818501915085601f8301126112ab57600080fd5b8135818111156112ba57600080fd5b8660208260051b85010111156112cf57600080fd5b60209290920196919550909350505050565b634e487b7160e01b600052601160045260246000fd5b60008261131457634e487b7160e01b600052601260045260246000fd5b500490565b8082028115828204841417611330576113306112e1565b92915050565b80820180821115611330576113306112e1565b81810381811115611330576113306112e1565b60006001820161136e5761136e6112e1565b5060010190565b634e487b7160e01b600052603260045260246000fdfea2646970667358221220c1ddaa41e507d389857121a6c6f81ba15a768d6683d0e5ff1b659d572f8abded64736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}