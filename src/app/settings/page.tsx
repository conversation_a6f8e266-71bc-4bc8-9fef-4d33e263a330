'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Loading } from '@/components/ui';
import {
  UserIcons,
  ActionIcons,
  FeatureIcons,
  NavigationIcons,
  NotificationIcons
} from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import { cn } from '@/lib/utils';

const SettingsPage = () => {
  const { user: telegramUser, logout: telegramLogout } = useTelegramAuth();
  const { user, isAuthenticated, isLoading: authLoading, logout } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'notifications'>('profile');
  const [isLoading, setIsLoading] = useState(false);

  // 确保只在客户端渲染
  useEffect(() => {
    setMounted(true);
  }, []);

  // 如果未认证，重定向到登录页
  useEffect(() => {
    if (mounted && !isAuthenticated && !authLoading) {
      console.log('❌ 用户未认证，重定向到登录页');
      window.location.href = '/login?redirectTo=/settings';
    }
  }, [mounted, isAuthenticated, authLoading]);

  const handleLogout = async () => {
    setIsLoading(true);
    await logout();
    window.location.href = '/';
  };

  const tabs = [
    {
      id: 'profile' as const,
      name: '个人资料',
      icon: UserIcons.user,
      description: '管理个人信息和偏好设置'
    },
    {
      id: 'security' as const,
      name: '安全设置',
      icon: UserIcons.shield,
      description: '账户安全和隐私设置'
    },
    {
      id: 'notifications' as const,
      name: '通知设置',
      icon: NotificationIcons.bell,
      description: '消息和提醒设置'
    }
  ];

  // 服务端渲染时显示加载状态
  if (!mounted || authLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-title-1 font-sf-pro font-bold text-label">
              设置
            </h1>
            <p className="text-body text-secondary-label mt-1">
              管理您的账户设置和偏好
            </p>
          </div>
          
          <Button
            variant="outline"
            size="md"
            onClick={() => window.history.back()}
            className="flex items-center space-x-2"
          >
            <Icon icon={NavigationIcons.back} size="sm" />
            <span>返回</span>
          </Button>
        </div>

        {/* 用户信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-system-blue to-system-purple rounded-full flex items-center justify-center">
                <Icon icon={UserIcons.user} size="xl" className="text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-title-3 font-sf-pro font-semibold text-label">
                  {user?.firstName} {user?.lastName}
                </h3>
                <p className="text-body text-secondary-label">
                  @{user?.username || 'telegram_user'}
                </p>
                <p className="text-caption-1 text-tertiary-label">
                  用户ID: {user?.id}
                </p>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* 标签页导航 */}
        <div className="mb-8">
          <div className="border-b border-system-gray-4">
            <nav className="flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    'flex items-center space-x-2 py-4 px-1 border-b-2 font-sf-pro font-medium text-sm whitespace-nowrap transition-colors',
                    activeTab === tab.id
                      ? 'border-system-blue text-system-blue'
                      : 'border-transparent text-secondary-label hover:text-label hover:border-system-gray-3'
                  )}
                >
                  <Icon icon={tab.icon} size="sm" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 标签页内容 */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-4">
                  个人资料
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-body font-sf-pro font-medium text-label mb-2">
                      显示名称
                    </label>
                    <div className="text-body text-secondary-label">
                      {user?.firstName} {user?.lastName}
                    </div>
                  </div>
                  <div>
                    <label className="block text-body font-sf-pro font-medium text-label mb-2">
                      用户名
                    </label>
                    <div className="text-body text-secondary-label">
                      @{user?.username || '未设置'}
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-4">
                  账户安全
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-body font-sf-pro font-medium text-label">
                        Telegram 认证
                      </p>
                      <p className="text-caption-1 text-secondary-label">
                        通过 Telegram 账户进行安全认证
                      </p>
                    </div>
                    <div className="flex items-center space-x-2 text-system-green">
                      <Icon icon={ActionIcons.checkCircle} size="sm" />
                      <span className="text-caption-1">已启用</span>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-4">
                  登录管理
                </h3>
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  size="md"
                  loading={isLoading}
                  className="flex items-center space-x-2 text-system-red border-system-red hover:bg-system-red hover:text-white"
                >
                  <Icon icon={ActionIcons.logout} size="sm" />
                  <span>退出登录</span>
                </Button>
              </Card>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-4">
                  通知偏好
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-body font-sf-pro font-medium text-label">
                        交易通知
                      </p>
                      <p className="text-caption-1 text-secondary-label">
                        接收交易状态更新和确认通知
                      </p>
                    </div>
                    <div className="text-caption-1 text-secondary-label">
                      即将推出
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-body font-sf-pro font-medium text-label">
                        价格提醒
                      </p>
                      <p className="text-caption-1 text-secondary-label">
                        当代币价格达到设定值时通知
                      </p>
                    </div>
                    <div className="text-caption-1 text-secondary-label">
                      即将推出
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default SettingsPage;
