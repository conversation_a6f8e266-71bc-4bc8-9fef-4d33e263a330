'use client';

import React, { useEffect, useState } from 'react';
import { Button, Icon } from '@/components/ui';
import { SocialIcons } from '@/config/icons';
import { forceNavigate } from '@/utils/navigation';

interface EmergencyLoginProps {
  onSuccess?: () => void;
  redirectTo?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
}

/**
 * 紧急登录组件 - 绕过所有SSR问题
 * 这是临时解决方案，用于快速修复登录跳转问题
 */
const EmergencyLogin: React.FC<EmergencyLoginProps> = ({ 
  onSuccess, 
  redirectTo = '/profile',
  className = '',
  size = 'lg',
  variant = 'primary'
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleEmergencyLogin = async () => {
    if (typeof window === 'undefined') return;
    
    setIsLoading(true);
    
    try {
      // 清除旧的认证数据
      localStorage.removeItem('telegram-user');
      
      // 模拟登录过程
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockUser = {
        id: Date.now(),
        firstName: '测试用户',
        lastName: 'Test',
        username: 'emergency_user',
        photoUrl: '',
      };
      
      // 保存到localStorage
      localStorage.setItem('telegram-user', JSON.stringify(mockUser));
      
      console.log('🚀 紧急登录成功，准备跳转到:', redirectTo);
      
      // 通知父组件
      onSuccess?.();
      
      // 强制跳转，确保绕过任何路由问题
      forceNavigate(redirectTo, 300);
      
    } catch (error) {
      console.error('紧急登录失败:', error);
      setIsLoading(false);
    }
  };

  // 服务端渲染时显示占位符
  if (!mounted) {
    return (
      <Button 
        disabled 
        variant={variant} 
        size={size} 
        className={`w-full ${className}`}
      >
        <Icon icon={SocialIcons.telegram} size="sm" className="mr-2" />
        加载中...
      </Button>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Button
        onClick={handleEmergencyLogin}
        disabled={isLoading}
        variant={variant}
        size={size}
        className="w-full"
      >
        {isLoading ? (
          <>
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
            登录中...
          </>
        ) : (
          <>
            <Icon icon={SocialIcons.telegram} size="sm" className="mr-2" />
            使用Telegram登录
          </>
        )}
      </Button>
      
      {process.env.NODE_ENV === 'development' && (
        <p className="text-caption-1 text-secondary-label text-center">
          🔧 紧急修复版本 - 绕过水合错误
        </p>
      )}
    </div>
  );
};

export default EmergencyLogin;
