'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface LoginRedirectProps {
  redirectTo: string;
  onComplete?: () => void;
}

/**
 * 登录后重定向组件
 * 确保认证状态更新后再执行跳转
 */
export const LoginRedirect: React.FC<LoginRedirectProps> = ({ 
  redirectTo, 
  onComplete 
}) => {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const [redirectAttempts, setRedirectAttempts] = useState(0);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || isLoading) return;

    console.log('🔄 LoginRedirect - 状态检查:', {
      isAuthenticated,
      isLoading,
      redirectTo,
      redirectAttempts
    });

    if (isAuthenticated && redirectAttempts < 3) {
      const attemptRedirect = () => {
        console.log(`🚀 执行跳转尝试 ${redirectAttempts + 1}/3 到:`, redirectTo);
        
        try {
          // 方法1: 使用Next.js router
          router.push(redirectTo);
          
          // 备选方法: 延迟后使用window.location
          setTimeout(() => {
            if (window.location.pathname !== redirectTo) {
              console.log('🔄 Router跳转可能失败，使用window.location备选方案');
              window.location.href = redirectTo;
            }
          }, 1000);
          
          setRedirectAttempts(prev => prev + 1);
          
          // 通知完成
          setTimeout(() => {
            onComplete?.();
          }, 1500);
          
        } catch (error) {
          console.error('❌ 跳转失败:', error);
          
          // 失败后使用强制跳转
          setTimeout(() => {
            window.location.href = redirectTo;
          }, 500);
        }
      };

      // 延迟执行跳转，确保状态稳定
      const timer = setTimeout(attemptRedirect, 300);
      
      return () => clearTimeout(timer);
    }
  }, [mounted, isAuthenticated, isLoading, redirectTo, redirectAttempts, router, onComplete]);

  if (!mounted || isLoading) {
    return (
      <div className="text-center">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-system-blue mx-auto mb-2"></div>
        <p className="text-sm text-secondary-label">正在验证认证状态...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="text-center">
        <p className="text-sm text-system-red">认证失败，请重新登录</p>
      </div>
    );
  }

  return (
    <div className="text-center">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-system-blue mx-auto mb-2"></div>
      <p className="text-sm text-secondary-label">
        登录成功，正在跳转到 {redirectTo}...
      </p>
      <p className="text-xs text-tertiary-label mt-1">
        尝试次数: {redirectAttempts}/3
      </p>
    </div>
  );
};
