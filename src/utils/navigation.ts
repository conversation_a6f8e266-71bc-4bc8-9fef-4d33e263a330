/**
 * 导航工具函数 - 解决水合错误导致的路由问题
 */

/**
 * 强制客户端跳转，避免 Next.js 路由问题
 */
export const forceNavigate = (url: string, delay: number = 100) => {
  if (typeof window === 'undefined') {
    return;
  }

  // 清除可能的错误状态
  console.log(`🔄 强制跳转到: ${url}`);

  // 立即尝试跳转，如果失败则使用延迟跳转
  try {
    window.location.href = url;
  } catch (error) {
    console.warn('立即跳转失败，使用延迟跳转:', error);
    setTimeout(() => {
      try {
        window.location.href = url;
      } catch (retryError) {
        console.error('延迟跳转也失败，尝试替换当前页面:', retryError);
        window.location.replace(url);
      }
    }, delay);
  }
};

/**
 * 安全的路由跳转，带重试机制
 */
export const safeNavigate = (url: string, maxRetries: number = 3) => {
  if (typeof window === 'undefined') {
    return;
  }
  
  let retries = 0;
  
  const attemptNavigation = () => {
    try {
      window.location.href = url;
    } catch (error) {
      console.error('导航失败:', error);
      retries++;
      
      if (retries < maxRetries) {
        console.log(`🔄 重试导航 (${retries}/${maxRetries})`);
        setTimeout(attemptNavigation, 500 * retries);
      } else {
        console.error('❌ 导航失败，已达到最大重试次数');
        // 最后尝试刷新页面
        window.location.reload();
      }
    }
  };
  
  attemptNavigation();
};

/**
 * 清除认证状态并跳转
 */
export const clearAuthAndNavigate = (url: string = '/') => {
  if (typeof window === 'undefined') {
    return;
  }
  
  // 清除所有可能的认证数据
  localStorage.removeItem('telegram-user');
  sessionStorage.clear();
  
  // 强制跳转
  forceNavigate(url);
};
